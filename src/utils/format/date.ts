import { differenceInYears, format, isToday, isYesterday } from "date-fns";

export const birthDate = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);
  const utcDate = new Date(parsedDate.getTime() + parsedDate.getTimezoneOffset() * 60000);
  const formattedDate = format(utcDate, "MMM dd, yyyy");
  const age = differenceInYears(new Date(), utcDate);

  return `${formattedDate} ( ${age} Yrs )`;
};

export const formatDate = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);
  return format(parsedDate, "dd/MM/yyyy, hh:mm a");
};

export const formatDateOnly = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);
  return format(parsedDate, "dd/MM/yyyy");
};

export const formatTime = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);
  return format(parsedDate, "hh:mm a");
};

export const formatDateWithToday = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);

  if (isToday(parsedDate)) {
    return `Today, ${format(parsedDate, "dd MMM yyyy")}`;
  }

  if (isYesterday(parsedDate)) {
    return `Yesterday, ${format(parsedDate, "dd MMM yyyy")}`;
  }

  return format(parsedDate, "EEEE, dd MMM yyyy");
};

export const formatNewDateFormat = (date: Date | string | number | undefined): string => {
  if (!date) return "";

  const parsedDate = new Date(date);
  return format(parsedDate, "dd MMM yyyy");
};
