import { ChangeEvent, useCallback, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid2 as Grid,
  IconButton,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";

import CustomAutoComplete from "@/common-components/custom-auto-complete/custom-auto-complete";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomLabel from "@/common-components/custom-label/custom-label";
import Paginator from "@/common-components/paginator/paginator";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
import Status from "@/common-components/status/status";
import { heading, tableCellCss } from "@/common-components/table/common-table-widgets";

import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { DeviceControllerService } from "@/sdk/requests/services.gen";
import { Device } from "@/sdk/requests/types.gen";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { theme } from "@/utils/theme";

// Headers for the devices table
const headerNames = ["Device Name", "Type", "Description", "Status"];

// Interface for device option format
interface DeviceOption {
  key: string;
  value: string;
}

// Interface for API response type
interface GetAllDevicesResponse {
  date?: string;
  code?: string;
  message?: unknown;
  data?: {
    content: Device[];
    page: {
      size: number;
      number: number;
      totalElements: number;
      totalPages: number;
    };
  };
  path?: string;
  requestId?: string;
  version?: string;
}

// Form type definition
interface DeviceFormValues {
  selectedDevice: string;
  syncEnabled: boolean;
}

type DevicesTabProps = {
  subdomain: string;
};

function DevicesTab(props: DevicesTabProps) {
  const { subdomain } = props;
  const xTenantIdVal = subdomain ? GetTenantId(subdomain) : "";
  const dispatch = useDispatch();

  // State for devices data
  const [devices, setDevices] = useState<Device[]>([]);
  const [deviceOptions, setDeviceOptions] = useState<DeviceOption[]>([]);

  // Pagination state
  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [searchDevice, setSearchDevice] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [totalElements, setTotalElements] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Dialog state
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // React Hook Form
  const { control, handleSubmit, reset } = useForm<DeviceFormValues>({
    defaultValues: {
      selectedDevice: "",
      syncEnabled: false,
    },
  });

  // Fetch devices for the current tenant
  const fetchDevices = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await DeviceControllerService.getAllDevices({
        xTenantId: xTenantIdVal,
        page,
        size,
        searchString: searchDevice,
      });

      // Type assertion for response
      const typedResponse = response as GetAllDevicesResponse;

      if (typedResponse.data?.content) {
        setDevices(typedResponse.data.content);
        setTotalElements(typedResponse.data.page.totalElements);
        setTotalPages(typedResponse.data.page.totalPages);
      } else {
        setDevices([]);
        setTotalElements(0);
        setTotalPages(0);
      }
    } catch (error) {
      setDevices([]);
      setTotalElements(0);
      setTotalPages(0);
    } finally {
      setIsLoading(false);
    }
  }, [xTenantIdVal, page, size, searchDevice]);

  // Fetch device options from eAmata tenant
  const fetchDeviceOptions = useCallback(async () => {
    try {
      const response = await DeviceControllerService.getAllDevices({
        xTenantId: "eAmata",
        size: 100,
        status: true,
        archive: false,
      });

      const typedResponse = response as GetAllDevicesResponse;

      if (typedResponse.data?.content) {
        const options = typedResponse.data.content
          .map((device) => ({
            key: device.uuid,
            value: device.name,
          }))
          .filter((option): option is DeviceOption => option.key !== undefined);
        setDeviceOptions(options);
      }
    } catch (error) {
      setDeviceOptions([]);
    }
  }, []);

  // Fetch devices on mount and when search/pagination params change
  useEffect(() => {
    if (xTenantIdVal) {
      fetchDevices();
    }
  }, [xTenantIdVal, fetchDevices]);

  // Fetch device options on mount
  useEffect(() => {
    fetchDeviceOptions();
  }, [fetchDeviceOptions]);

  // CSS styles
  const typographyCss = {
    fontFamily: "Roboto",
    fontWeight: 400,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "0%",
    color: "#212D30",
  };

  // Handle page change
  const handlePageChange = (_event: ChangeEvent<unknown> | null, newPage: number) => {
    setPage(newPage);
  };

  // Handle records per page change
  const handleRecordsPerPageChange = (newSize: number) => {
    setSize(newSize);
    setPage(0);
  };

  // Handle dialog open and close
  const handleOpenAddDialog = () => {
    setOpenAddDialog(true);
  };

  const handleCloseAddDialog = () => {
    setOpenAddDialog(false);
    reset();
  };

  // Handle save device
  const handleSaveDevice = handleSubmit(async (data) => {
    try {
      setIsSaving(true);
      // Get the device details from eAmata tenant using the selected device ID
      const deviceDetails = await DeviceControllerService.getDeviceById({
        deviceUuid: data.selectedDevice,
        xTenantId: "eAmata",
      });

      // Create the device in the current tenant using the retrieved details
      const response = await DeviceControllerService.createDevice({
        xTenantId: xTenantIdVal,
        requestBody: {
          name: deviceDetails?.data?.name as string,
          deviceType: deviceDetails?.data?.deviceType as string,
          description: deviceDetails?.data?.description as string,
          guideLink: deviceDetails?.data?.guideLink as string,
          active: true,
        },
      });

      // Display the success message from the API response
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message: typeof response?.message === "string" ? response.message : "Device added successfully!",
        })
      );
      fetchDevices();
      handleCloseAddDialog();
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity)?.body?.message || "Failed to add device",
        })
      );
    } finally {
      setIsSaving(false);
    }
  });

  return (
    <Grid border={"1px solid #EAECF0"} borderRadius={2}>
      {/* Header section */}
      <Grid container mb={2} px={2} pt={2} display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h6" fontWeight={600}>
          Devices
        </Typography>

        <Box display="flex" alignItems="center" gap={2}>
          <CustomInput
            placeholder="Search Device"
            hasStartSearchIcon={true}
            name="searchDevice"
            value={searchDevice}
            onDebounceCall={(searchString: string) => setSearchDevice(searchString)}
            onInputEmpty={() => setSearchDevice("")}
          />

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            sx={{
              bgcolor: "#006D8F",
              "&:hover": { bgcolor: "#005a75" },
              textTransform: "none",
              width: "200px",
            }}
            onClick={handleOpenAddDialog}
          >
            Add Device
          </Button>
        </Box>
      </Grid>
      {/* Table section */}
      <Grid>
        <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
          <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {headerNames.map((header, index) => (
                  <TableCell
                    sx={{
                      ...heading,
                    }}
                    align="left"
                    key={index}
                  >
                    <Typography fontWeight={550} variant="bodySmall" color="#667085" sx={{ fontStyle: "Roboto" }}>
                      {header}
                    </Typography>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                [...Array(5)].map((_, index) => (
                  <TableRow key={index}>
                    {[...Array(headerNames.length)].map((_, cellIndex) => (
                      <TableCell key={cellIndex}>
                        <Skeleton variant="text" width={100} />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : devices.length > 0 ? (
                devices.map((device, index) => (
                  <TableRow key={index} hover>
                    <TableCell>
                      <Typography
                        sx={{
                          cursor: "pointer",
                          color: theme.palette.primary.main,
                          fontWeight: 550,
                          fontSize: "14px",
                        }}
                        variant="bodySmall"
                      >
                        {device.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>{device.deviceType}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography sx={typographyCss}>{device.description}</Typography>
                    </TableCell>
                    <TableCell>
                      <Status status={device.active ? "ACTIVE" : "INACTIVE"} width="74px" />
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography>No devices found</Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <Grid container justifyContent={"flex-end"} p={2}>
          <Paginator
            page={page}
            totalPages={totalPages}
            totalRecord={totalElements}
            onPageChange={handlePageChange}
            onRecordsPerPageChange={handleRecordsPerPageChange}
          />
        </Grid>
      </Grid>
      {/* Add Device Dialog */}
      <Dialog
        open={openAddDialog}
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            width: "500px",
            height: "300px",
            overflow: "visible",
          },
        }}
      >
        <DialogTitle sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", pb: 1 }}>
          <Typography variant="h6" fontWeight={600}>
            Add Device
          </Typography>
          <IconButton onClick={handleCloseAddDialog} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <Divider />

        <DialogContent sx={{ pb: 3, pt: 2, overflow: "visible" }}>
          <Box mb={3}>
            <FormControl fullWidth>
              <Controller
                name="selectedDevice"
                control={control}
                rules={{ required: "Device is required" }}
                render={({ field }) => (
                  <Grid container direction="column">
                    <CustomLabel label="Device" isRequired />
                    <CustomAutoComplete
                      placeholder="Select Device"
                      options={deviceOptions}
                      value={field.value}
                      onChange={(newValue: string) => field.onChange(newValue)}
                    />
                  </Grid>
                )}
              />
            </FormControl>
          </Box>
        </DialogContent>
        <Divider />
        <DialogActions sx={{ p: 2 }}>
          <Button
            onClick={handleSaveDevice}
            variant="contained"
            disabled={isSaving}
            sx={{
              bgcolor: "#006D8F",
              "&:hover": { bgcolor: "#005a75" },
              textTransform: "none",
              borderRadius: 1,
              px: 4,
            }}
          >
            {isSaving ? "Saving..." : "Save"}
          </Button>
        </DialogActions>
      </Dialog>{" "}
    </Grid>
  );
}

export default DevicesTab;
