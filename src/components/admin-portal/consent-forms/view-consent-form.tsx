import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

import { Link, Typography } from "@mui/material";
import { Box, Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import { useDrawer } from "@/components/providers/DrawerProvider";
import { setIsLoading } from "@/redux/actions/loader-action";
import { ConsentFormControllerService, ConsentFormTemplate } from "@/sdk/requests";

type ViewConsentFormType = {
  open: boolean;
  onClose: () => void;
  title: string;
  uuid: string;
  xTenantIdVal: string;
};

const ViewConsentForm = (props: ViewConsentFormType) => {
  const { open, title, uuid, xTenantIdVal } = props;
  const { open: openDrawer } = useDrawer();
  const [formUrl, setFormUrl] = useState("");
  const dispatch = useDispatch();
  const {
    data: dataFormById,
    isLoading: isLoadingFormById,
    isSuccess: isSuccessFormById,
    refetch: refetchFormById,
    isRefetching,
  } = useQuery({
    enabled: !!uuid,
    queryKey: ["forms-by-id", uuid],
    queryFn: () =>
      ConsentFormControllerService.getConsentFormId({
        consentFormId: uuid || "",
        xTenantId: xTenantIdVal || "",
      }),
  });

  useEffect(() => {
    if (isSuccessFormById) {
      const res = ((dataFormById as unknown as AxiosResponse).data as ConsentFormTemplate).document;
      setFormUrl(res);
    }
  }, [isSuccessFormById, dataFormById]);

  useEffect(() => {
    dispatch(setIsLoading(isLoadingFormById || isRefetching));
  }, [dispatch, isLoadingFormById, isRefetching]);

  const handleRefetchForm = () => {
    if (uuid && !formUrl) {
      refetchFormById();
    }
  };

  useEffect(() => {
    if (open && formUrl) {
      handleDrawer.viewConsentForm(title, formUrl);
    }
  }, [open, formUrl]);

  const handleDrawer = {
    viewConsentForm: (title: string, formUrl: string) => {
      openDrawer({
        title: title,
        component: (
          <Box
            sx={{
              background: "white",
              height: "100%",
              width: "100%",
              borderRadius: "20px",
            }}
          >
            <Grid container flexDirection={"column"} width={"100%"} height={"100%"}>
              <Grid width={"100%"} height={"100%"}>
                {formUrl && <iframe style={{ width: "100%", height: "100%", border: "none" }} src={formUrl}></iframe>}
                {!formUrl && (
                  <Grid container p={2} columnGap={2}>
                    <Typography variant="bodyMedium" fontWeight={550}>
                      Consent Form loading. Please wait for few seconds.
                    </Typography>
                    <Link
                      style={{
                        color: "#667085",
                        textDecoration: "none",
                        cursor: "pointer",
                      }}
                      onClick={() => handleRefetchForm()}
                    >
                      <Typography variant="bodyMedium" fontWeight={550}>
                        Click here to refetch.
                      </Typography>
                    </Link>
                  </Grid>
                )}
              </Grid>
            </Grid>
          </Box>
        ),
      });
    },
  };

  return null;
};

export default ViewConsentForm;
