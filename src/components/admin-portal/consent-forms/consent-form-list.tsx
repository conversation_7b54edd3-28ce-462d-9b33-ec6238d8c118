import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";

import { Link, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import CustomInput from "../../../common-components/custom-input/custom-input";
import Paginator from "../../../common-components/paginator/paginator";
import Status from "../../../common-components/status/status";
import { heading, linkCss, tableCellCss } from "../../../common-components/table/common-table-widgets";
import { TableHeaders } from "../../../common-components/table/table-models";
import { ContentObject } from "../../../models/response/response-content-entity";
import { setIsLoading } from "../../../redux/actions/loader-action";
import { ConsentFormControllerService, ConsentFormTemplate } from "../../../sdk/requests";
import { GetTenantId } from "../../../services/common/get-tenant-id";
import { theme } from "../../../utils/theme";
import ViewConsentForm from "./view-consent-form";

export const headers: TableHeaders[] = [{ header: "Form Name" }, { header: "Status" }];

type ConsentFormProps = {
  subdomain: string;
};

const ConsentForm = (props: ConsentFormProps) => {
  const { subdomain } = props;

  const xTenantIdVal = subdomain ? GetTenantId(subdomain) : "";
  const [totalPages, setTotalPages] = useState(0);
  const [selectedForm, setSelectedForm] = useState<ConsentFormTemplate>();
  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [open, setOpen] = useState(false);

  const [searchString, setSearchstring] = useState<string>("");
  const [sortBy] = useState("");
  const [sortDirection] = useState("desc");
  const [rows, setRows] = useState<ConsentFormTemplate[]>([]);
  const [totalElements, setTotalElements] = useState<number>(0);
  const dispatch = useDispatch();
  const [status] = useState<boolean | undefined>(undefined);
  const [archive] = useState<boolean | undefined>(undefined);

  const { data, isLoading, isSuccess } = useQuery({
    queryKey: ["list-of-forms", page, size, searchString],
    queryFn: () =>
      ConsentFormControllerService.getAllConsentFormTemplate({
        page,
        size,
        sortBy,
        sortDirection,
        archive,
        searchString,
        status,
        xTenantId: xTenantIdVal || "",
      }),
    enabled: !!subdomain,
  });

  useEffect(() => {
    if (isSuccess) {
      const response = (data as unknown as AxiosResponse).data as ContentObject<ConsentFormTemplate[]>;

      const formData = response?.content;
      setTotalPages(response?.page?.totalPages as number);
      setTotalElements(response?.page?.totalElements as number);

      const tablePayload = formData?.map((form) => {
        return {
          name: form.name,
          active: form.active,
          uuid: form.uuid,
        } as unknown as ConsentFormTemplate;
      });

      setRows(tablePayload);
    }
  }, [data, isSuccess]);

  const handlePageChange = (_event: ChangeEvent<unknown> | null, page: number) => {
    setPage(page);
  };

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handleSorting = (header: string) => {
    header;
  };

  const handleOnClickForm = async (form: ConsentFormTemplate) => {
    await setSelectedForm(form);
    setOpen(true);
  };

  useEffect(() => {
    dispatch(setIsLoading(isLoading || isLoading));
  }, [dispatch, isLoading, isLoading]);

  return (
    <Grid height={"100%"}>
      <Grid
        border={`1px solid ${theme.palette.grey[300]}`}
        boxShadow={`0px 0px 16px 0px #021D2614`}
        height={"100%"}
        borderRadius={"8px"}
        container
        flexDirection={"column"}
      >
        <Grid container p={2} justifyContent={"space-between"}>
          <Grid container alignItems={"center"} columnGap={2}>
            <Typography variant="bodyMedium" fontWeight={550}>
              Consent Forms
            </Typography>
          </Grid>

          <Grid container columnGap={2}>
            <Grid>
              <CustomInput
                hasStartSearchIcon
                placeholder={"Search By Name"}
                name={"searchString"}
                value={searchString}
                onDebounceCall={(debouncedValue) => setSearchstring(debouncedValue)}
                onInputEmpty={() => setSearchstring("")}
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Table */}
        <Grid>
          <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
            <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
              <TableHead>
                <TableRow>
                  {headers.map((header, index) => (
                    <TableCell
                      sx={{
                        ...heading,
                        minWidth: header.minWidth ? header.minWidth : "inherit",
                        maxWidth: header.maxWidth ? header.maxWidth : "inherit",
                      }}
                      align="left"
                      key={index}
                    >
                      {header.header === "Name" ? (
                        <Link
                          style={{
                            color: "#667085",
                            textDecoration: "none",
                            cursor: "pointer",
                          }}
                          onClick={() => handleSorting(header.header)}
                        >
                          <Typography fontWeight={550} variant="bodySmall">
                            {header.header}
                          </Typography>
                        </Link>
                      ) : (
                        <Grid
                          container
                          flexDirection={"column"}
                          alignContent={header.header === "Status" ? `flex-end` : "flex-start"}
                        >
                          <Typography variant="bodySmall">{header.header}</Typography>
                        </Grid>
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                <>
                  {rows.length > 0 ? (
                    rows.map((item: ConsentFormTemplate, index: number) => (
                      <>
                        <TableRow key={index}>
                          <TableCell sx={{ ...heading }} align="left">
                            <Grid container flexDirection={"column"}>
                              <Link
                                style={{
                                  ...linkCss,
                                }}
                                onClick={() => handleOnClickForm(item)}
                              >
                                <Typography fontWeight={550} variant="bodySmall">
                                  {item.name}
                                </Typography>
                              </Link>
                            </Grid>
                          </TableCell>
                          <TableCell sx={{ ...heading }} align="left">
                            <Grid container flexDirection={"column"} alignContent={`flex-end`}>
                              <Status status={`${item.active ? "ACTIVE" : "INACTIVE"}`} width="74px" />
                            </Grid>
                          </TableCell>
                        </TableRow>
                      </>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={2} align="center">
                        <Typography variant="bodySmall" fontWeight={550}>
                          No records found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </>
              </TableBody>
            </Table>
          </TableContainer>

          <Grid container>
            <Paginator
              page={page}
              totalPages={totalPages}
              totalRecord={totalElements}
              onRecordsPerPageChange={handleRecordsPerPageChange}
              onPageChange={handlePageChange}
            />
          </Grid>
        </Grid>
        {selectedForm?.uuid && (
          <ViewConsentForm
            open={open}
            onClose={function (): void {
              setOpen(false);
              setSelectedForm({} as ConsentFormTemplate);
            }}
            title={selectedForm?.name || "View form"}
            uuid={selectedForm?.uuid || ""}
            xTenantIdVal={xTenantIdVal}
          />
        )}
      </Grid>
    </Grid>
  );
};

export default ConsentForm;
