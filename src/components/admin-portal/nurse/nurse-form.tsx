import { <PERSON><PERSON><PERSON><PERSON>, useFieldArray, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import { Box, Button, Grid2 as Grid, IconButton, InputLabel, LinearProgress, Stack, Typography } from "@mui/material";

import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import DrawerBody from "@/components/ui/DrawerBody";
import DrawerFooter from "@/components/ui/DrawerFooter";
import Autocomplete from "@/components/ui/Form/Autocomplete";
import { Datepicker } from "@/components/ui/Form/Datepicker";
import { Input } from "@/components/ui/Form/Input";
import { InputPhoneNumber } from "@/components/ui/Form/InputPhoneNumber";
import Select from "@/components/ui/Form/Select";
import { UploadImage } from "@/components/ui/Form/UploadImage";
import { Gender } from "@/constants/roles";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { ContentObject } from "@/models/response/response-content-entity";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useProviderControllerServiceCreateProvider } from "@/sdk/queries";
import { LicenseStateControllerService, Provider, ProviderControllerService } from "@/sdk/requests";
import { splitPhoneNumber } from "@/services/common/phone-formatter";
import { stateList } from "@/utils/StateList";
import { theme } from "@/utils/theme";

import { manualEntryFormSchema } from "./nurse-schema";

type NurseFormProps = {
  nurse: Provider | null;
  isEdit?: boolean;
  handleDrawerClose: () => void;
};

const NurseForm = (props: NurseFormProps) => {
  const { nurse, handleDrawerClose, isEdit } = props;
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const { data: licensedStateOptions } = useQuery({
    queryKey: ["list-of-licensed-states"],
    queryFn: async () => {
      const response = await LicenseStateControllerService.getAllLicensedStates({
        page: 0,
        size: 100,
      });

      const { content } = response.data as ContentObject<{ id: number; country: "US"; state: string }[]>;

      return content?.map((item) => {
        return {
          key: item.state.toString(),
          value: item.state,
          info: item.id.toString(),
        };
      });
    },
  });

  const getIdOfState = (state: string) => licensedStateOptions?.find((item) => item.value === state)?.info || "";

  const { data: profile, isLoading: loadingProfile } = useQuery({
    queryKey: ["provider", nurse?.uuid],
    queryFn: async () => {
      const response = (await ProviderControllerService.getProviderById({
        providerUuid: nurse?.uuid || "",
      })) as AxiosResponse<Provider>;

      return response.data as Provider;
    },
    enabled: Boolean(nurse?.uuid) && isEdit,
  });

  const initialValues = {
    firstName: nurse?.firstName || "",
    lastName: nurse?.lastName || "",
    npi: nurse?.npi || "",
    email: nurse?.email || "",
    phone: nurse?.phone ? splitPhoneNumber(nurse?.phone)?.number : "" || "",
    prefix: nurse?.phone ? splitPhoneNumber(nurse?.phone)?.countryCode : "+1",
    address: {
      line1: nurse?.address?.line1 || "",
      line2: nurse?.address?.line2 || "",
      city: nurse?.address?.city || "",
      state: nurse?.address?.state || "",
      zipcode: nurse?.address?.zipcode || "",
      country: nurse?.address?.country || "USA",
    },
    avatar: "",
    licenses: nurse?.providerLicenseDetails?.map((v) => ({
      licenseNumber: v.licenseNumber || "",
      licensedState: v.licensedStates?.length ? `${v.licensedStates[0].state}` : "",
      licenseExpiryDate: v.expiryDate ? new Date(v.expiryDate) : new Date(),
    })) || [{ licenseNumber: "", licensedState: "", licenseExpiryDate: "" as unknown as Date }],
    status: !isEdit ? "active" : nurse?.active ? "active" : "inactive" || "",
    gender: nurse?.gender || "",
  };

  const formMethods = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(manualEntryFormSchema),
  });

  const {
    fields: licenseFields,
    append: appendLicenses,
    remove: removeLicenses,
  } = useFieldArray({
    name: "licenses",
    control: formMethods.control,
  });

  const handleOnSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["list-of-staff"] });
    if (isEdit) {
      queryClient.invalidateQueries({ queryKey: ["nurse", nurse?.uuid] });
    }
    dispatch(
      setSnackbarOn({
        severity: AlertSeverity.SUCCESS,
        message: isEdit ? "Nurse updated successfully!" : "Nurse added successfully!",
      })
    );
    handleDrawerClose();
  };

  const addNurseService = useProviderControllerServiceCreateProvider({
    onSuccess: handleOnSuccess,
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const editNurseService = useMutation({
    mutationFn: ({ user, avatar }: { user: Provider; avatar?: string }) =>
      Promise.all([
        ProviderControllerService.updateProvider({
          requestBody: { ...user, uuid: nurse?.uuid },
        }),
        avatar !== "" &&
          ProviderControllerService.changeAvatar({
            requestBody: {
              newAvatar: avatar?.startsWith("data:image")
                ? avatar.replace(/data:image\/(jpeg|png);base64,/, "")
                : avatar,
            },
            providerUuid: nurse?.uuid || "",
          }),
      ]),
    onSuccess: handleOnSuccess,
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const onSubmit = async (values: unknown) => {
    const formValues = values as typeof initialValues;
    const payload = {
      chatbotTone: nurse?.chatbotTone,
      introduction: nurse?.introduction,
      firstName: formValues.firstName,
      lastName: formValues.lastName,
      npi: formValues.npi,
      email: formValues.email,
      phone: `${formValues.prefix}${formValues.phone}`,
      active: formValues.status === "active",
      address: {
        line1: formValues.address.line1,
        line2: formValues.address.line2,
        city: formValues.address.city,
        country: formValues.address.country,
        state: formValues.address.state,
        zipcode: formValues.address.zipcode,
      },
      providerLicenseDetails: formValues.licenses.map(
        (item: { licenseExpiryDate?: Date; licensedState?: string; licenseNumber?: string }) => {
          return {
            licenseNumber: item.licenseNumber || "",
            expiryDate: item?.licenseExpiryDate || "",
            licensedStates: [
              {
                state: item.licensedState,
                id: item?.licensedState ? +getIdOfState(item?.licensedState) : "",
              },
            ],
          };
        }
      ),
      gender: formValues.gender,
      role: "NURSE",
    } as Provider;

    if (isEdit) {
      editNurseService.mutate({ user: payload, avatar: formValues.avatar });
    } else {
      addNurseService.mutate({ requestBody: payload });
    }
  };

  if (loadingProfile) {
    return (
      <DrawerBody>
        <LinearProgress />
      </DrawerBody>
    );
  }

  return (
    <DrawerBody padding={3} offset={80}>
      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)}>
          <Stack spacing={3}>
            <Grid container spacing={2}>
              {isEdit && (
                <Grid size={{ xs: 12, sm: 2 }}>
                  <UploadImage name="avatar" defaultImage={profile?.avatar as string} isLoading={loadingProfile} />
                </Grid>
              )}
              <Grid container size={isEdit ? 10 : 12}>
                <Grid size={6}>
                  <Input name="firstName" isRequired />
                </Grid>
                <Grid size={6}>
                  <Input name="lastName" isRequired />
                </Grid>
                <Grid size={6}>
                  <Input
                    name="email"
                    type="email"
                    isRequired
                    disabled={Boolean(profile?.emailVerified)}
                    isVerified={Boolean(profile?.emailVerified)}
                  />
                </Grid>
                <Grid size={6}>
                  <InputPhoneNumber isRequired />
                </Grid>
                <Grid size={6}>
                  <Input
                    name="npi"
                    label="NPI"
                    placeholder="Enter NPI Number"
                    isRequired
                    disabled={Boolean(profile?.emailVerified)}
                    inputProps={{ maxLength: 10 }}
                  />
                </Grid>
                <Grid size={6}>
                  <Select name="gender" options={Gender} placeholder="Select Gender" isRequired />
                </Grid>
                {isEdit && (
                  <Grid size={6}>
                    <Select
                      name="status"
                      options={[
                        { value: "active", label: "Active" },
                        { value: "inactive", label: "Inactive" },
                      ]}
                      placeholder="Select Status"
                      isRequired
                    />
                  </Grid>
                )}
              </Grid>
            </Grid>
            <Box sx={{ border: `1px solid ${theme.palette.divider}`, borderRadius: 2 }}>
              <Box sx={{ padding: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
                <Typography variant="medium">Address</Typography>
              </Box>
              <Grid container spacing={2} sx={{ padding: 2 }}>
                <Grid container spacing={2}>
                  <Grid size={6}>
                    <Input name="address.line1" label="Line 1" isRequired />
                  </Grid>
                  <Grid size={6}>
                    <Input name="address.line2" label="Line 2" />
                  </Grid>
                  <Grid size={6}>
                    <Autocomplete
                      name="address.state"
                      label="State"
                      options={stateList.map((item) => ({ label: item.value, value: item.key }))}
                      placeholder="Select State"
                      isRequired
                    />
                  </Grid>
                  <Grid size={6}>
                    <Input name="address.city" label="City" isRequired />
                  </Grid>
                  <Grid size={6}>
                    <Input name="address.country" label="Country" isRequired disabled />
                  </Grid>
                  <Grid size={6}>
                    <Input name="address.zipcode" label="Zip Code" isRequired />
                  </Grid>
                </Grid>
              </Grid>
            </Box>
            <Box>
              <Typography variant="medium">License Details</Typography>
              <Box sx={{ backgroundColor: "#EEFBFF", padding: 1.5, marginTop: 1.5 }}>
                {licenseFields.map((license, index) => (
                  <Box sx={{ display: "flex", gap: 1, marginBottom: 2 }} key={license.id}>
                    <Grid size="grow">
                      <Grid container spacing={2}>
                        <Grid size={4}>
                          <Input
                            name={`licenses.${index}.licenseNumber`}
                            label="Licensed Number"
                            placeholder="Enter License Number"
                            isRequired
                          />
                        </Grid>
                        <Grid size={4}>
                          <Autocomplete
                            name={`licenses.${index}.licensedState`}
                            label="Licensed State"
                            options={
                              licensedStateOptions
                                ? licensedStateOptions.map((item) => ({ label: item.value, value: item.key }))
                                : []
                            }
                            placeholder="Select State"
                            isRequired
                          />
                        </Grid>
                        <Grid size={4}>
                          <Datepicker
                            name={`licenses.${index}.licenseExpiryDate`}
                            label="License Expiry Date"
                            isRequired
                            minDate={new Date()}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid size="auto">
                      <InputLabel sx={{ mb: 1 }}>&nbsp;</InputLabel>
                      <IconButton onClick={() => removeLicenses(index)} disabled={licenseFields.length <= 1}>
                        <DeleteOutlineOutlinedIcon />
                      </IconButton>
                    </Grid>
                  </Box>
                ))}
                <Button
                  sx={{
                    "&.MuiButton-outlined": {
                      borderColor: "#078EB9",
                      color: "#078EB9",
                    },
                    "&.MuiButton-outlined:hover": {
                      backgroundColor: "transparent",
                      opacity: 0.8,
                    },
                  }}
                  variant="outlined"
                  startIcon={<AddOutlinedIcon />}
                  onClick={() =>
                    appendLicenses({
                      licensedState: "",
                      licenseExpiryDate: "" as unknown as Date,
                      licenseNumber: "",
                    })
                  }
                >
                  Add License Details
                </Button>
              </Box>
            </Box>
          </Stack>
          <DrawerFooter>
            <Button variant="contained" type="submit" loading={addNurseService.isPending || editNurseService.isPending}>
              {isEdit ? "Save Nurse" : "Add Nurse"}
            </Button>
          </DrawerFooter>
        </form>
      </FormProvider>
    </DrawerBody>
  );
};

export default NurseForm;
