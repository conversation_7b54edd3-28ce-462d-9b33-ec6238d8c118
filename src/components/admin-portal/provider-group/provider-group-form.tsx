import React, { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import { Box, Button, Divider, Grid2 as Grid, InputAdornment, Stack, Typography } from "@mui/material";
import FormControl from "@mui/material/FormControl";
import FormControlLabel from "@mui/material/FormControlLabel";
import Radio from "@mui/material/Radio";
import RadioGroup from "@mui/material/RadioGroup";

import { yupResolver } from "@hookform/resolvers/yup";
import { useQueryClient } from "@tanstack/react-query";

import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import DrawerBody from "@/components/ui/DrawerBody";
import DrawerFooter from "@/components/ui/DrawerFooter";
import Autocomplete from "@/components/ui/Form/Autocomplete";
import { Input } from "@/components/ui/Form/Input";
import { InputPhoneNumber } from "@/components/ui/Form/InputPhoneNumber";
import Select from "@/components/ui/Form/Select";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import {
  useProviderGroupControllerServiceCreateProviderGroup,
  useProviderGroupControllerServiceUpdateProviderGroup,
} from "@/sdk/queries";
import {
  EhrControllerService,
  EhrProviderControllerService,
  ProviderGroup,
  ProviderGroupControllerService,
} from "@/sdk/requests";
import { splitPhoneNumber } from "@/services/common/phone-formatter";
import { stateList } from "@/utils/StateList";
import { getNumbersOnly } from "@/utils/format/string";
import { theme } from "@/utils/theme";

import { manualEntryFormSchema } from "./provider-group-schema";

type ProviderGroupFormType = {
  selectedPG: ProviderGroup | undefined;
  handleDrawerClose: () => void;
  isEdit?: boolean;
  refetch?: () => void;
};

type EhrProvider = {
  id: number;
  name: string;
};

const ProviderGroupForm = (props: ProviderGroupFormType) => {
  const { selectedPG, handleDrawerClose, isEdit, refetch } = props;
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const [valueRadio, setValueRadio] = useState("manualEntry");
  const [ehrProviderGroup, setEhrProviderGroup] = useState<ProviderGroup>();
  const [isLoading, setIsLoading] = useState(false);
  const [ehrProviders, setEhrProviders] = useState<EhrProvider[]>([]);

  const getSubDomain = (url: string) => {
    if (!url) {
      return "";
    }
    const regex = /https:\/\/(.*?)\./;
    const match = url.match(regex);
    if (match && match[1]) {
      return match[1];
    }
    return "";
  };

  const extractEnvironment = (url: string) => {
    const match = url.match(/^https:\/\/(\w+)\.admin\.eamata\.com/);
    return match ? match[1] : "dev";
  };

  const initialValues = {
    optionSelected: valueRadio,
    name: selectedPG?.name || "",
    email: selectedPG?.email || "",
    phone: selectedPG?.phone ? splitPhoneNumber(selectedPG?.phone)?.number : "",
    prefix: selectedPG?.phone ? splitPhoneNumber(selectedPG?.phone)?.countryCode : "+1",
    npi: selectedPG?.npi || "",
    address: {
      line1: selectedPG?.address?.line1 || "",
      line2: selectedPG?.address?.line2 || "",
      city: selectedPG?.address?.city || "",
      state: selectedPG?.address?.state || "",
      zipcode: selectedPG?.address?.zipcode || "",
      country: selectedPG?.address?.country || "USA",
    },
    subDomain: selectedPG?.subdomain ? getSubDomain(selectedPG?.subdomain) : "",
    ehrSystem: selectedPG?.ehrName || "",
    ehr: "AthenaHealth",
    practiceId: "",
    status: selectedPG?.active ? "active" : "inactive",
    notificationEmail: selectedPG?.notificationEmail || "",
    ehrName: ehrProviderGroup?.ehrName || "",
    ehrEmail: ehrProviderGroup?.email || "",
    ehrSubDomain: "",
    ehrPrefix: "+1",
    ehrPhone: ehrProviderGroup?.phone || "",
    ehrNpi: ehrProviderGroup?.npi || "",
    ehrnotificationEmail: ehrProviderGroup?.notificationEmail || "",
    ehrAddress: {
      line1: ehrProviderGroup?.address.line1 || "",
      line2: ehrProviderGroup?.address.line2 || "",
      city: ehrProviderGroup?.address.city || "",
      state: ehrProviderGroup?.address.state || "",
      country: ehrProviderGroup?.address.country || "USA",
      zipcode: ehrProviderGroup?.address.zipcode || "",
    },
    ehrId: ehrProviderGroup?.ehrId || "",
    billingCycle: selectedPG?.billingCycle || "",
    monitoringThreshold: selectedPG?.monitoringThreshold || 0,
  };

  const formMethods = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(manualEntryFormSchema),
  });

  const handleOnSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["list-of-pg"] });
    queryClient.invalidateQueries({ queryKey: ["pg-by-id", selectedPG?.uuid] });
    dispatch(
      setSnackbarOn({
        severity: AlertSeverity.SUCCESS,
        message: isEdit ? "Provider group updated successfully!" : "Provider group added successfully!",
      })
    );
    handleDrawerClose();
    refetch && refetch();
  };

  const addProviderGroupService = useProviderGroupControllerServiceCreateProviderGroup({
    onSuccess: handleOnSuccess,
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const editProviderGroupService = useProviderGroupControllerServiceUpdateProviderGroup({
    onSuccess: handleOnSuccess,
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const getNewProviderGroup = async () => {
    setIsLoading(true);
    try {
      const res = await EhrControllerService.getOrganizationByPracticeId({
        practiceId: formMethods.getValues("practiceId") || "",
      });
      if (res) {
        setEhrProviderGroup(res);

        formMethods.setValue("ehrName", res?.name || "");
        formMethods.setValue("ehrEmail", res?.email || "");
        formMethods.setValue("ehrPhone", res?.phone ? getNumbersOnly(res?.phone) : "");
        formMethods.setValue("ehrNpi", res?.npi || "");
        formMethods.setValue("ehrAddress", {
          line1: res.address.line1 || "",
          line2: res.address.line2 || "",
          city: res.address.city || "",
          state: res.address.state || "",
          country: res.address.country || "USA",
          zipcode: res.address.zipcode || "",
        });
        formMethods.setValue("ehrSubDomain", res?.subdomain || "");
        formMethods.setValue("ehrId", res?.ehrId || "");
        formMethods.setValue("billingCycle", res?.billingCycle || "");
        formMethods.setValue("monitoringThreshold", res?.monitoringThreshold || 0);
      }
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity).body.message || "Invalid FHIR Id",
        })
      );
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (values: unknown) => {
    let payload: ProviderGroup = {} as ProviderGroup;
    const formValues = values as typeof initialValues;

    if (formValues.optionSelected === "manualEntry") {
      payload = {
        name: formValues.name,
        email: formValues.email,
        phone: `${formValues.prefix}${formValues.phone}` || "",
        address: {
          line1: formValues.address.line1,
          line2: formValues.address.line2,
          city: formValues.address.city,
          state: formValues.address.state,
          country: "USA",
          zipcode: formValues.address.zipcode,
        },
        npi: formValues.npi,
        subdomain: formValues.subDomain || "",
        active: !isEdit ? true : formValues.status === "active" ? true : false,
        notificationEmail: formValues.notificationEmail || "",
        ehrName: formValues.ehrName || "",
        billingCycle: formValues.billingCycle,
        monitoringThreshold: formValues.monitoringThreshold,
      } as ProviderGroup;
    } else {
      payload = {
        name: formValues.ehrName || "",
        email: formValues.ehrEmail || "",
        phone: formValues.ehrPhone ? `${formValues.ehrPrefix}${formValues.ehrPhone}` : "",
        address: {
          line1: formValues.ehrAddress.line1 || "",
          line2: formValues.ehrAddress.line2 || "",
          city: formValues.ehrAddress.city || "",
          state: formValues.ehrAddress.state || "",
          country: formValues.ehrAddress.country || "USA",
          zipcode: formValues.ehrAddress.zipcode || "",
        },
        npi: formValues.ehrNpi || "",
        subdomain: formValues.ehrSubDomain || "",
        active: Boolean(ehrProviderGroup?.active),
        ehrId: formValues.ehrId || "",
        notificationEmail: formValues.ehrnotificationEmail || "",
        ehrName: formValues.ehrName || "",
      } as ProviderGroup;
    }
    if (isEdit) {
      await editProviderGroupService.mutateAsync({
        requestBody: { ...payload, uuid: selectedPG?.uuid },
      });
    } else {
      await addProviderGroupService.mutateAsync({ requestBody: payload });
    }
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const optionSelected = (event.target as HTMLInputElement).value;

    formMethods.setValue("optionSelected", optionSelected);
    setValueRadio(optionSelected);
  };

  // Fetch provider group data when editing
  useEffect(() => {
    const fetchProviderGroupData = async () => {
      if (isEdit && selectedPG?.uuid) {
        setIsLoading(true);
        try {
          const response = await ProviderGroupControllerService.getProviderGroupById({
            providerGroupId: selectedPG.uuid,
            xTenantId: "eAmata",
          });

          if (response && response.data) {
            const providerGroupData = response.data as ProviderGroup;

            formMethods.setValue("name", providerGroupData.name || "");
            formMethods.setValue("email", providerGroupData.email || "");

            if (providerGroupData.ehrName) {
              formMethods.setValue("ehrName", providerGroupData.ehrName);
            }

            if (providerGroupData.phone) {
              const phoneDetails = splitPhoneNumber(providerGroupData.phone);
              formMethods.setValue("phone", phoneDetails?.number || "");
              formMethods.setValue("prefix", phoneDetails?.countryCode || "+1");
            }

            formMethods.setValue("npi", providerGroupData.npi || "");

            if (providerGroupData.address) {
              const address = {
                ...formMethods.getValues("address"),
                line1: providerGroupData.address.line1 || "",
                line2: providerGroupData.address.line2 || "",
                city: providerGroupData.address.city || "",
                state: providerGroupData.address.state || "",
                zipcode: providerGroupData.address.zipcode || "",
              };
              formMethods.setValue("address", address);
            }

            formMethods.setValue(
              "subDomain",
              providerGroupData.subdomain ? getSubDomain(providerGroupData.subdomain) : ""
            );
            formMethods.setValue("status", providerGroupData.active ? "active" : "inactive");
            formMethods.setValue("notificationEmail", providerGroupData.notificationEmail || "");
            formMethods.setValue("billingCycle", providerGroupData.billingCycle || "");
            formMethods.setValue("monitoringThreshold", providerGroupData.monitoringThreshold || 0);
          }
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchProviderGroupData();
  }, [isEdit, selectedPG?.uuid]);

  // Fetch EHR providers
  useEffect(() => {
    const fetchEhrProviders = async () => {
      const response = await EhrProviderControllerService.getAllEhrProviders();
      if (response && response.data) {
        setEhrProviders(Array.isArray(response.data) ? response.data : []);
      }
    };

    fetchEhrProviders();
  }, []);

  const billingCycles = [
    {
      label: "No Billing",
      value: "NO_BILLING",
    },
    {
      label: "30 Days",
      value: "ROLLING_30_DAYS",
    },
    {
      label: "Calendar Month",
      value: "CALENDAR_MONTH",
    },
  ];

  return (
    <DrawerBody padding={3} offset={80}>
      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)}>
          <Stack spacing={3}>
            <Grid container spacing={2}>
              {!isEdit && (
                <Grid size={12}>
                  <FormControl sx={{ paddingBottom: 1 }}>
                    <RadioGroup
                      row
                      aria-labelledby="demo-controlled-radio-buttons-group"
                      name="controlled-radio-buttons-group"
                      value={valueRadio}
                      onChange={handleChange}
                    >
                      <FormControlLabel value="manualEntry" control={<Radio />} label="Manual Entry" />
                      <FormControlLabel value="enrollFromEHR" control={<Radio />} label="Enroll From EHR" />
                    </RadioGroup>
                  </FormControl>
                  <Divider />
                </Grid>
              )}
              {valueRadio === "manualEntry" && (
                <>
                  <Grid size={6}>
                    <Input name="name" isRequired />
                  </Grid>
                  <Grid size={6}>
                    <Input name="email" isRequired />
                  </Grid>
                  <Grid size={6}>
                    <InputPhoneNumber isRequired />
                  </Grid>
                  <Grid size={6}>
                    <Input name="npi" label="NPI" placeholder="Enter NPI Number" isRequired />
                  </Grid>
                  <Grid size={6}>
                    <Input
                      name="subDomain"
                      isRequired
                      startAdornment={
                        <InputAdornment sx={{ ml: 1.5 }} position="start">
                          https://{""}
                        </InputAdornment>
                      }
                      endAdornment={
                        <InputAdornment position="end" sx={{ mr: 1.5 }}>
                          <Typography variant="bodySmall">
                            {`.${extractEnvironment(window.location.href)}.care.eamata.com`}
                          </Typography>
                        </InputAdornment>
                      }
                    />
                  </Grid>
                  <Grid container size={6}>
                    <Input name="notificationEmail" placeholder="Enter Notification Email" />
                  </Grid>
                  <Grid size={isEdit ? 6 : 12}>
                    <Select
                      name="ehrName"
                      label="EHR System"
                      options={ehrProviders.map((ehr) => ({
                        label: ehr.name,
                        value: ehr.name,
                      }))}
                      placeholder="Select EHR system"
                    />
                  </Grid>
                  <Grid container spacing={2} size={12}>
                    <Grid size={6}>
                      <Select
                        isRequired
                        name="billingCycle"
                        label="Billing Cycle"
                        options={billingCycles}
                        placeholder="Select Billing Cycle"
                      />
                    </Grid>
                    <Grid size={6}>
                      <Input
                        name="monitoringThreshold"
                        isRequired
                        endAdornment={
                          <InputAdornment position="end" sx={{ mr: 1.5 }}>
                            <Typography variant="bodySmall">{`sec`}</Typography>
                          </InputAdornment>
                        }
                      />
                    </Grid>
                  </Grid>
                  {isEdit && (
                    <Grid size={6}>
                      <Select
                        name="status"
                        options={[
                          { value: "active", label: "Active" },
                          { value: "inactive", label: "Inactive" },
                        ]}
                        placeholder="Select Status"
                        isRequired
                      />
                    </Grid>
                  )}
                  <Box sx={{ border: `1px solid ${theme.palette.divider}`, borderRadius: 2 }}>
                    <Box sx={{ padding: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
                      <Typography variant="medium">Address</Typography>
                    </Box>
                    <Grid container spacing={2} sx={{ padding: 2 }}>
                      <Grid size={6}>
                        <Input name="address.line1" label="Line 1" isRequired />
                      </Grid>
                      <Grid size={6}>
                        <Input name="address.line2" label="Line 2" />
                      </Grid>
                      <Grid size={6}>
                        <Autocomplete
                          name="address.state"
                          label="State"
                          options={stateList.map((item) => ({ label: item.key, value: item.value }))}
                          placeholder="Select State"
                          isRequired
                        />
                      </Grid>
                      <Grid size={6}>
                        <Input name="address.city" label="City" isRequired />
                      </Grid>
                      <Grid size={6}>
                        <Input name="address.country" label="Country" isRequired disabled />
                      </Grid>
                      <Grid size={6}>
                        <Input name="address.zipcode" label="Zip Code" isRequired />
                      </Grid>
                    </Grid>
                  </Box>
                </>
              )}

              {valueRadio === "enrollFromEHR" && (
                <Grid container sx={{ width: "100%" }}>
                  <Grid size={6}>
                    <Select
                      name="ehr"
                      label="EHR"
                      options={[{ value: "epicHealthcare", label: "Epic Healthcare" }]}
                      placeholder="Select EHR"
                      isRequired
                    />
                  </Grid>
                  <Grid size={6}>
                    <Input name="practiceId" label="Practice ID" isRequired />
                  </Grid>
                  <Grid size={12} sx={{ display: "flex", justifyContent: "flex-end" }}>
                    <Button
                      variant="contained"
                      onClick={() => getNewProviderGroup()}
                      loading={isLoading}
                      disabled={!(formMethods.watch("practiceId") && formMethods.watch("ehr"))}
                    >
                      <Typography variant="bodySmall">Fetch Details</Typography>
                    </Button>
                  </Grid>
                  {ehrProviderGroup && (
                    <>
                      <Grid size={12}>
                        <Grid>
                          <Typography variant="bodyMedium" fontWeight={600}>
                            Result
                          </Typography>
                          <Divider sx={{ my: 1.5 }} />
                        </Grid>
                        <Stack spacing={2}>
                          <Grid container spacing={2}>
                            <Grid size={6}>
                              <Input
                                name="ehrName"
                                isRequired
                                placeholder="Enter Name"
                                value={ehrProviderGroup?.name || ""}
                                label="Name"
                                disabled={!!ehrProviderGroup.name}
                              />
                            </Grid>
                            <Grid size={6}>
                              <Input
                                disabled={!!ehrProviderGroup.email}
                                name="ehrEmail"
                                isRequired
                                placeholder="Enter Email"
                                label="Email"
                              />
                            </Grid>
                          </Grid>

                          <Grid container spacing={2}>
                            <Grid size={6}>
                              <InputPhoneNumber
                                namePrefix="ehrPrefix"
                                namePhone="ehrPhone"
                                isRequired
                                placeholder="Enter Phone Number"
                              />
                            </Grid>
                            <Grid size={6}>
                              <Input
                                name="ehrNpi"
                                disabled={!!ehrProviderGroup.npi}
                                isRequired
                                placeholder="Enter Npi"
                                label="NPI"
                              />
                            </Grid>
                          </Grid>
                          <Grid container spacing={1}>
                            <Grid size={6}>
                              <Input
                                name="ehrSubDomain"
                                label="Sub Domain"
                                isRequired
                                startAdornment={
                                  <InputAdornment sx={{ ml: 1.5 }} position="start">
                                    https://{""}
                                  </InputAdornment>
                                }
                                endAdornment={
                                  <InputAdornment position="end" sx={{ mr: 1.5 }}>
                                    <Typography variant="bodySmall">
                                      {`.${extractEnvironment(window.location.href)}.care.eamata.com`}
                                    </Typography>
                                  </InputAdornment>
                                }
                              />
                            </Grid>
                            <Grid size={6}>
                              <Input
                                name="ehrnotificationEmail"
                                label="Notification Email"
                                placeholder="Enter Notification Email"
                                disabled={!!ehrProviderGroup.notificationEmail}
                              />
                            </Grid>
                          </Grid>
                        </Stack>
                      </Grid>
                      <Box sx={{ border: `1px solid ${theme.palette.divider}`, borderRadius: 2 }}>
                        <Box sx={{ padding: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
                          <Typography variant="medium">Address</Typography>
                        </Box>
                        <Grid container spacing={2} sx={{ padding: 2 }}>
                          <Grid size={6}>
                            <Input name="ehrAddress.line1" label="Line 1" isRequired />
                          </Grid>
                          <Grid size={6}>
                            <Input name="ehrAddress.line2" label="Line 2" />
                          </Grid>
                          <Grid size={6}>
                            <Autocomplete
                              name="ehrAddress.state"
                              label="State"
                              options={stateList.map((item) => ({ label: item.key, value: item.value }))}
                              placeholder="Select State"
                              isRequired
                            />
                          </Grid>
                          <Grid size={6}>
                            <Input name="ehrAddress.city" label="City" isRequired />
                          </Grid>
                          <Grid size={6}>
                            <Input name="ehrAddress.country" label="Country" isRequired disabled />
                          </Grid>
                          <Grid size={6}>
                            <Input name="ehrAddress.zipcode" label="Zip Code" isRequired />
                          </Grid>
                        </Grid>
                      </Box>
                    </>
                  )}
                </Grid>
              )}
            </Grid>
          </Stack>
          <DrawerFooter>
            <Button
              variant="contained"
              type="submit"
              loading={addProviderGroupService.isPending || editProviderGroupService.isPending}
            >
              {isEdit ? "Save Provider Group" : "Add Provider Group"}
            </Button>
          </DrawerFooter>
        </form>
      </FormProvider>
    </DrawerBody>
  );
};

export default ProviderGroupForm;
