import * as yup from "yup";

import {
  addressLine1Max128ErrorMsg,
  addressLine1RequiredErrorMsg,
  cityMax64ErrorMsg,
  cityRequiredErrorMsg,
  cityStateRegexErrorMsg,
  countryRequiredErrorMsg,
  ehrRequiredErrorMsg,
  emailRegexErrorMsg,
  emailRequiredErrorMsg,
  lessThan255ErrorMsg,
  nameRequiredErrorMsg,
  npiRegexErrorMsg,
  npiRequiredErrorMsg,
  phoneRegexErrorMsg,
  phoneRequiredErrorMsg,
  practiceIdRequiredErrorMsg,
  stateRequiredErrorMsg,
  subdomainRequiredErrorMsg,
  zipCodeRegexErrorMsg,
  zipCodeRequiredErrorMsg,
} from "@/constants/error-messages";
import { cityStateRgex, emailRegExp, npiRegExp, phoneRegex, zipCodeRegex } from "@/utils/regex";

export const manualEntryFormSchema = yup.object().shape({
  optionSelected: yup.string(),
  name: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.string().required(nameRequiredErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  email: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.string().required(emailRequiredErrorMsg).matches(emailRegExp, emailRegexErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  phone: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup
        .string()
        .required(phoneRequiredErrorMsg)
        .transform((value) => (value === "" ? null : value))
        .matches(phoneRegex, phoneRegexErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  prefix: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup
        .string()
        .required(phoneRequiredErrorMsg)
        .transform((value) => (value === "" ? null : value));
    } else {
      return yup.string().notRequired();
    }
  }),
  npi: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.string().required(npiRequiredErrorMsg).matches(npiRegExp, npiRegexErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  address: yup.object().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.object().shape({
        line1: yup.string().max(128, addressLine1Max128ErrorMsg).required(addressLine1RequiredErrorMsg),
        line2: yup.string().max(128, addressLine1Max128ErrorMsg),
        city: yup
          .string()
          .matches(cityStateRgex, cityStateRegexErrorMsg)
          .max(64, cityMax64ErrorMsg)
          .required(cityRequiredErrorMsg),
        state: yup
          .string()
          .matches(cityStateRgex, cityStateRegexErrorMsg)
          .max(50, lessThan255ErrorMsg)
          .required(stateRequiredErrorMsg),
        zipcode: yup.string().required(zipCodeRequiredErrorMsg).matches(zipCodeRegex, zipCodeRegexErrorMsg),
        country: yup.string().required(countryRequiredErrorMsg),
      });
    } else {
      return yup.object().notRequired();
    }
  }),
  subDomain: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup
        .string()
        .required(subdomainRequiredErrorMsg)
        .matches(
          /^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/,
          "Subdomain must only contain lowercase letters, numbers, and hyphens, and must not start or end with a hyphen."
        );
    } else {
      return yup.string().notRequired();
    }
  }),
  notificationEmail: yup
    .string()
    .notRequired()
    .test({
      name: "email-format",
      message: emailRegexErrorMsg,
      test: function (value) {
        if (!value) return true;
        return emailRegExp.test(value);
      },
    }),
  status: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.string();
    } else {
      return yup.string().notRequired();
    }
  }),
  // =>> Ehr field
  ehr: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.string().required(ehrRequiredErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  practiceId: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.string().required(practiceIdRequiredErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  ehrName: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.string().required(nameRequiredErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  ehrEmail: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.string().required(emailRequiredErrorMsg).matches(emailRegExp, emailRegexErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  ehrPhone: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup
        .string()
        .required(phoneRequiredErrorMsg)
        .transform((value) => (value === "" ? null : value))
        .matches(phoneRegex, phoneRegexErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  ehrPrefix: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup
        .string()
        .required(phoneRequiredErrorMsg)
        .transform((value) => (value === "" ? null : value));
    } else {
      return yup.string().notRequired();
    }
  }),
  ehrNpi: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.string().required(npiRequiredErrorMsg).matches(npiRegExp, npiRegexErrorMsg);
    } else {
      return yup.string().notRequired();
    }
  }),
  ehrAddress: yup.object().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.object().shape({
        line1: yup.string().max(128, addressLine1Max128ErrorMsg).required(addressLine1RequiredErrorMsg),
        line2: yup.string().max(128, addressLine1Max128ErrorMsg),
        city: yup
          .string()
          .matches(cityStateRgex, cityStateRegexErrorMsg)
          .max(64, cityMax64ErrorMsg)
          .required(cityRequiredErrorMsg),
        state: yup
          .string()
          .matches(cityStateRgex, cityStateRegexErrorMsg)
          .max(50, lessThan255ErrorMsg)
          .required(stateRequiredErrorMsg),
        zipcode: yup.string().required(zipCodeRequiredErrorMsg).matches(zipCodeRegex, zipCodeRegexErrorMsg),
        country: yup.string().required(countryRequiredErrorMsg),
      });
    } else {
      return yup.object().notRequired();
    }
  }),
  ehrId: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup.string().nullable();
    } else {
      return yup.string().notRequired();
    }
  }),
  billingCycle: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.string().nullable();
    } else {
      return yup.string().notRequired();
    }
  }),
  monitoringThreshold: yup.number().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "manualEntry") {
      return yup.number().nullable();
    } else {
      return yup.number().notRequired();
    }
  }),
  ehrSubDomain: yup.string().when("optionSelected", (optionSelected) => {
    if (optionSelected[0] === "enrollFromEHR") {
      return yup
        .string()
        .required(subdomainRequiredErrorMsg)
        .matches(
          /^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/,
          "Subdomain must only contain lowercase letters, numbers, and hyphens, and must not start or end with a hyphen."
        );
    } else {
      return yup.string().notRequired();
    }
  }),
  ehrnotificationEmail: yup
    .string()
    .notRequired()
    .test({
      name: "email-format",
      message: emailRegexErrorMsg,
      test: function (value) {
        if (!value) return true;
        return emailRegExp.test(value);
      },
    }),
});
