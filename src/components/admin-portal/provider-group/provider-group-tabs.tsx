import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";

import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import FmdGoodOutlinedIcon from "@mui/icons-material/FmdGoodOutlined";
import InsertLinkIcon from "@mui/icons-material/InsertLink";
import LocalPhoneIcon from "@mui/icons-material/LocalPhone";
import MailOutlineIcon from "@mui/icons-material/MailOutline";
import ModeEditOutlineOutlinedIcon from "@mui/icons-material/ModeEditOutlineOutlined";
import { Button, ButtonBase, Divider, Link, Tab, Tabs, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import { CustomTabPanel, a11yProps } from "@/common-components/custom-tab/custom-tab";
import Status from "@/common-components/status/status";

import { useDrawer } from "@/components/providers/DrawerProvider";
import { setIsLoading } from "@/redux/actions/loader-action";
import { ProviderGroup, ProviderGroupControllerService } from "@/sdk/requests";
import { theme } from "@/utils/theme";

import DevicesTab from "../Devices/DevicesTab";
import ConsentForm from "../consent-forms/consent-form-list";
import LocationList from "../locations/location-list";
import ProviderList from "../provider/provider-list";
import StaffList from "../staff/staff-list";
import ProviderGroupForm from "./provider-group-form";

const tabLabels = ["Locations", "Providers", "Staff", "Consent Forms", "Devices"];

const ProviderGroupTabs = () => {
  const { open: openDrawer, close: closeDrawer } = useDrawer();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  const { providerGroupId } = useParams();

  const activeTab = parseInt(searchParams.get("tab") || "0");
  const [value, setValue] = useState(activeTab);
  const [providerGroup, setProviderGroup] = useState<ProviderGroup>({} as ProviderGroup);

  // const providerGroupDetails = useMemo(
  //   () => (location?.state?.providerGroup || {}) as ProviderGroup,
  //   [location?.state?.providerGroup]
  // );
  // const [locationData, setLocationDate] = useState<ProviderGroup>({} as ProviderGroup);
  // useEffect(() => {
  //   setLocationDate(providerGroupDetails);
  // }, [providerGroupDetails]);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSearchParams({ tab: newValue.toString() });
    setValue(newValue);
  };

  const handleOnClickLink = async () => {
    window.open(providerGroup.subdomain);
  };

  const { data, isSuccess, isLoading, isRefetching } = useQuery({
    queryKey: ["pg-by-id", providerGroupId],
    queryFn: () =>
      ProviderGroupControllerService.getProviderGroupById({
        providerGroupId: providerGroupId || "",
      }),
    enabled: !!providerGroupId,
  });

  useEffect(() => {
    if (isSuccess) {
      setProviderGroup((data as unknown as AxiosResponse).data);
    }
  }, [isSuccess, data]);

  useEffect(() => {
    dispatch(setIsLoading(isLoading || isRefetching));
  }, [dispatch, isLoading, isRefetching]);

  const handleDrawer = {
    providerGroupForm: (title: string) => {
      openDrawer({
        title: title,
        component: <ProviderGroupForm selectedPG={providerGroup} isEdit={true} handleDrawerClose={closeDrawer} />,
      });
    },
  };

  return (
    <Grid height={"100%"} p={2} width={"100%"} maxWidth={"100%"} overflow={"auto"}>
      <Grid height={"100%"} borderRadius={"8px"} container flexDirection={"column"}>
        <Grid container p={2} columnGap={3} borderRadius={"8px"} bgcolor={theme.palette.secondary.light}>
          <Grid container alignItems={"flex-start"} columnGap={2}>
            <Grid container flexDirection={"column"} rowGap={1}>
              <Grid container alignItems={"center"} columnGap={1}>
                <Typography variant="bodyMedium" fontWeight={550}>
                  {providerGroup ? ` ${providerGroup?.name}` : ""}
                </Typography>
              </Grid>
              <Grid container columnGap={2} alignItems={"center"}>
                <Typography variant="bodyMedium">{providerGroup?.npi}</Typography>
                <Status status={providerGroup?.active ? "ACTIVE" : "INACTIVE"} width="fit-content" />
              </Grid>
            </Grid>
            <Divider sx={{ margin: "0px 10px" }} orientation="vertical" />
          </Grid>
          <Grid container flexDirection={"column"} rowGap={1}>
            <Grid container width={"fit-content"} columnGap={1}>
              <MailOutlineIcon fontSize="small" />
              <Typography variant="bodySmall">{providerGroup?.email || "-"}</Typography>
            </Grid>
            <Grid container width={"fit-content"} columnGap={1}>
              <LocalPhoneIcon fontSize="small" />
              <Typography variant="bodySmall">{providerGroup?.phone || "-"}</Typography>
            </Grid>
          </Grid>
          <Grid container flexDirection={"column"} rowGap={1}>
            <Grid container width={"fit-content"} columnGap={1}>
              <Link style={{ cursor: "pointer" }} onClick={() => handleOnClickLink()}>
                <InsertLinkIcon fontSize="small" color="inherit" />
              </Link>
              <Typography variant="bodySmall">{providerGroup?.subdomain || "-"}</Typography>
            </Grid>
            <Grid container width={"fit-content"} columnGap={1}>
              <FmdGoodOutlinedIcon fontSize="small" />
              <Typography variant="bodySmall" maxWidth={"600px"} noWrap>
                {providerGroup?.address?.line1
                  ? `${providerGroup?.address?.line1 || "-"},  ${providerGroup?.address?.line2 || "-"},
								 ${providerGroup?.address?.city || "-"}, ${providerGroup?.address?.state || "-"},
								  ${providerGroup?.address?.country || "-"}, ${providerGroup?.address?.zipcode || "-"}`
                  : "-"}
              </Typography>
            </Grid>
          </Grid>
          <Grid flex={1} container flexDirection={"column"} alignItems={"flex-end"}>
            <Grid>
              <Button
                variant="outlined"
                onClick={() =>
                  handleDrawer.providerGroupForm(
                    providerGroup?.name ? `${providerGroup.name}: Edit` : `Edit Provider Group`
                  )
                }
                sx={{ padding: "15px" }}
              >
                <ModeEditOutlineOutlinedIcon sx={{ marginRight: "10px" }} fontSize="small" />
                <Typography variant="bodySmall">Edit Provider Group</Typography>
              </Button>
              <Grid container alignItems={"center"} justifyContent={"center"}>
                <Grid mt={0.5}>
                  <ButtonBase onClick={() => navigate("/admin/provider-group")}>
                    <ArrowBackOutlinedIcon sx={{ marginRight: "10px" }} fontSize="small" color="primary" />
                    <Typography color="primary" fontWeight={550} variant="bodySmall">
                      {"Back to list"}
                    </Typography>
                  </ButtonBase>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <Grid>
          <Grid sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs value={value} onChange={handleChange}>
              {tabLabels?.map((item, index) => (
                <Tab sx={{ textTransform: "none", fontWeight: 550 }} key={index} label={item} {...a11yProps(0)} />
              ))}
            </Tabs>
          </Grid>
          <Grid flex={1}>
            {tabLabels.map((item, index) => (
              <CustomTabPanel key={index} value={value} index={index}>
                {item === "Locations" && (
                  <LocationList subdomain={providerGroup.subdomain} isPGActive={!!providerGroup.active} />
                )}
                {item === "Providers" && <ProviderList subdomain={providerGroup.subdomain} />}
                {item === "Staff" && (
                  <StaffList
                    subdomain={providerGroup.subdomain}
                    filterOptions={["Provider Group Admin", "Site Admin", "Front Desk", "Biller"]}
                    roleType={"STAFF"}
                    listType={"PG"}
                    isPGActive={!!providerGroup.active}
                  />
                )}
                {item === "Consent Forms" && <ConsentForm subdomain={providerGroup.subdomain} />}
                {item === "Devices" && <DevicesTab subdomain={providerGroup.subdomain} />}
              </CustomTabPanel>
            ))}
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default ProviderGroupTabs;
