import React, { ChangeEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";

import AddIcon from "@mui/icons-material/Add";
import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import RestoreIcon from "@mui/icons-material/Restore";
import {
  Button,
  ButtonBase,
  IconButton,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomInput from "@/common-components/custom-input/custom-input";
import Paginator from "@/common-components/paginator/paginator";
import Status from "@/common-components/status/status";
import {
  heading,
  iconStyles,
  linkCss,
  tableCellCss,
  tableCss,
  typographyCss,
} from "@/common-components/table/common-table-widgets";
import { TableHeaders } from "@/common-components/table/table-models";

import { useDrawer } from "@/components/providers/DrawerProvider";
import { LOG_LEVEL } from "@/constants/config";
import useApiFeedback from "@/hooks/useApiFeedback";
import { ContentObject } from "@/models/response/response-content-entity";
import { setIsLoading } from "@/redux/actions/loader-action";
import { useProviderGroupControllerServiceUpdateProviderGroupArchiveStatus } from "@/sdk/queries";
import { ProviderGroup, ProviderGroupControllerService } from "@/sdk/requests";
import { ConsoleLogger } from "@/services/common/logger.service";

import ProviderGroupForm from "./provider-group-form";

export const headers: TableHeaders[] = [
  { header: "Name" },
  { header: "Email" },
  { header: "Phone Number" },
  { header: "NPI" },
  { header: "Sub-domain" },
  { header: "Status" },
  { header: "Actions" },
];

export type pgType = {
  Name: string;
  Email: string;
  Phone: string;
  NPI: string;
  status?: string;
};

const ProviderGroupList = () => {
  const { open: openDrawer, close: closeDrawer } = useDrawer();
  const logger = new ConsoleLogger({ level: LOG_LEVEL });
  const navigate = useNavigate();
  const [totalPages, setTotalPages] = React.useState<number>(0);
  const [page, setPage] = React.useState(0);
  const [size, setSize] = React.useState(10);
  const [searchString, setSearchstring] = useState<string>("");
  const [sortBy, setSortBy] = useState("");
  const [sortDirections, setSortDirections] = useState("desc");
  const [sortDirection, setSortDirection] = useState("desc");
  const [sortDirectionByStatus, setSortDirectionByStatus] = useState("desc");

  const [rows, setRows] = React.useState<ProviderGroup[]>([]);
  const [totalElements, setTotalElements] = useState<number>(0);
  const dispatch = useDispatch();
  const [openConfirmDeletePopUp, setOpenConfirmDeletePopUp] = useState(false);
  const [selectedPG, setSelectedPG] = useState<ProviderGroup>();

  const handleOnClickLink = (uuid: string, pg: ProviderGroup) => {
    logger.log("On click of link", uuid);
    navigate(`${pg.uuid}`, { state: { providerGroup: pg } });
  };
  const [openConfirmRestorePopUp, setOpenConfirmRestorePopUp] = useState(false);

  const {
    data,
    isLoading,
    isSuccess,
    refetch,
    //
    isRefetching,
  } = useQuery({
    queryKey: ["list-of-pg", page, size, searchString, sortDirection, sortBy],
    queryFn: () =>
      ProviderGroupControllerService.getAllProviderGroups({
        page,
        size,
        sortBy,
        sortDirection,
        searchString,
      }),
  });

  useEffect(() => {
    if (isSuccess) {
      const response = (data as unknown as AxiosResponse).data as ContentObject<ProviderGroup[]>;

      const pgData = response?.content;
      const totalResponseData = response?.page?.totalPages;
      setTotalPages(totalResponseData as number);
      setTotalElements(response?.page?.totalElements as number);

      const tablePayload = pgData?.map((pg) => {
        return {
          uuid: pg?.uuid,
          name: pg.name || "",
          address: {
            line1: pg.address.line1,
            line2: pg.address.line2,
            city: pg.address.city,
            state: pg.address.state,
            country: pg.address.country,
            zipcode: pg.address.zipcode,
          },
          phone: pg?.phone,
          active: pg?.active,
          npi: pg?.npi,
          subdomain: pg?.subdomain,
          email: pg?.email,
          archive: pg.archive,
          notificationEmail: pg.notificationEmail,
        } as ProviderGroup;
      });

      setRows(tablePayload);
    }
  }, [data, isSuccess]);

  const handlePageChange = (_event: ChangeEvent<unknown> | null, page: number) => {
    setPage(page);
  };

  const {
    mutateAsync: mutateAsyncArchive,
    isSuccess: isSuccessArchive,
    isError: isErrorArchive,
    error: errorArchive,
    data: dataArchive,
  } = useProviderGroupControllerServiceUpdateProviderGroupArchiveStatus();

  useApiFeedback(
    isErrorArchive,
    errorArchive,
    isSuccessArchive,
    (dataArchive?.message || "Provider group status updated successfully!") as string
  );

  const confirmRestore = async () => {
    await mutateAsyncArchive({
      providerGroupId: selectedPG?.uuid || "",
      status: false,
    });
    await refetch();
    setOpenConfirmRestorePopUp(false);
  };

  const confirmDelete = async () => {
    await mutateAsyncArchive({
      providerGroupId: selectedPG?.uuid || "",
      status: true,
    });
    await refetch();
    setOpenConfirmDeletePopUp(false);
  };

  useEffect(() => {
    dispatch(setIsLoading(isLoading || isRefetching));
  }, [dispatch, isLoading, isRefetching]);

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handleSorting = (column: string) => {
    if (column == "Name") {
      setSortBy("name");
      setSortDirections((prev) => (prev === "desc" ? "asc" : "desc"));
    } else if (column === "Status") {
      setSortBy("active");
      setSortDirectionByStatus((prev) => (prev === "desc" ? "asc" : "desc"));
    }
  };

  useEffect(() => {
    if (sortBy == "name") {
      setSortDirection(sortDirections);
    } else if (sortBy === "active") {
      setSortDirection(sortDirectionByStatus);
    }
  }, [sortBy, sortDirection, handleSorting]);

  const handleDrawer = {
    providerGroupForm: (title: string, pg?: ProviderGroup) => {
      openDrawer({
        title: title,
        component: (
          <ProviderGroupForm selectedPG={pg} isEdit={!!pg} handleDrawerClose={closeDrawer} refetch={refetch} />
        ),
      });
    },
  };

  return (
    <Grid height={"100%"} p={2}>
      <Grid height={"100%"} borderRadius={"8px"} container flexDirection={"column"}>
        <Grid container p={2} justifyContent={"space-between"}>
          <Grid container alignItems={"center"} columnGap={2}>
            <ButtonBase>
              {/* <ArrowBackOutlinedIcon sx={{ marginRight: "10px" }} /> */}
              <Typography variant="bodyMedium" fontWeight={550}>
                Provider Groups
              </Typography>
            </ButtonBase>
          </Grid>
          <Grid container columnGap={2}>
            <Grid>
              <CustomInput
                hasStartSearchIcon
                placeholder={"Search by Name and NPI"}
                name={"searchString"}
                value={searchString}
                onDebounceCall={(debouncedValue) => setSearchstring(debouncedValue)}
                onInputEmpty={() => setSearchstring("")}
              />
            </Grid>
            <Button
              startIcon={<AddIcon />}
              variant="contained"
              onClick={() => {
                handleDrawer.providerGroupForm("Add Provider Group");
              }}
            >
              <Typography variant="bodySmall">New Provider Group</Typography>
            </Button>
          </Grid>
        </Grid>
        <Grid sx={{ ...tableCss }}>
          <TableContainer sx={{ maxHeight: "75vh", overflow: "auto" }}>
            <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
              <TableHead>
                <TableRow>
                  {headers.map((header, index) => (
                    <TableCell
                      sx={{
                        ...heading,
                        minWidth: header.minWidth ? header.minWidth : "inherit",
                        maxWidth: header.maxWidth ? header.maxWidth : "inherit",
                      }}
                      align="left"
                      key={index}
                    >
                      {header.header === "Name" ? (
                        <Link
                          style={{
                            color: "#667085",
                            textDecoration: "none",
                            cursor: "pointer",
                          }}
                          onClick={() => handleSorting(header.header)}
                        >
                          <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                            {header.header}
                            <Typography mt={0.3}>
                              {sortDirections == "asc" ? (
                                <ArrowUpwardIcon fontSize="small" />
                              ) : (
                                <ArrowDownwardIcon fontSize="small" />
                              )}
                            </Typography>
                          </Typography>
                        </Link>
                      ) : header.header === "Status" ? (
                        <Link
                          style={{
                            color: "#667085",
                            textDecoration: "none",
                            cursor: "pointer",
                          }}
                          onClick={() => handleSorting(header.header)}
                        >
                          <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                            {header.header}
                            <Typography mt={0.3}>
                              {sortDirectionByStatus == "asc" ? (
                                <ArrowUpwardIcon fontSize="small" />
                              ) : (
                                <ArrowDownwardIcon fontSize="small" />
                              )}
                            </Typography>
                          </Typography>
                        </Link>
                      ) : (
                        <Grid
                          container
                          flexDirection={"column"}
                          alignContent={header.header === "Actions" ? `flex-end` : "flex-start"}
                        >
                          <Typography variant="bodySmall">{header.header}</Typography>
                        </Grid>
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {rows.length > 0 ? (
                  rows.map((pg: ProviderGroup, index: number) => (
                    <>
                      <TableRow key={index}>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"}>
                            <Link
                              style={{
                                ...linkCss,
                                textDecoration: "none",
                              }}
                              onClick={() => handleOnClickLink("", pg)}
                            >
                              <Typography fontWeight={550} color="primary" variant="bodySmall">
                                {pg.name}
                              </Typography>
                            </Link>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {pg.email}
                            </Typography>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {pg.phone}
                            </Typography>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {pg.npi}
                            </Typography>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {pg.subdomain}
                            </Typography>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"}>
                            <Status status={pg.active ? "ACTIVE" : "INACTIVE"} width="74px" />
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="right">
                          <Grid container justifyContent={"flex-end"} columnGap={1.2} flexWrap={"nowrap"}>
                            <IconButton
                              onClick={() => {
                                setSelectedPG(pg);
                                handleDrawer.providerGroupForm(
                                  pg?.name ? `${pg?.name}: Edit` : `Edit Provider Group`,
                                  pg
                                );
                              }}
                              sx={{ padding: "0px 5px" }}
                              aria-label="edit"
                            >
                              <EditOutlinedIcon sx={iconStyles} />
                            </IconButton>
                            {!pg.archive ? (
                              <IconButton
                                aria-label="delete"
                                onClick={() => {
                                  setSelectedPG(pg);
                                  setOpenConfirmDeletePopUp(true);
                                }}
                                sx={{ padding: "0px" }}
                              >
                                <ArchiveOutlinedIcon sx={iconStyles} />
                              </IconButton>
                            ) : (
                              <IconButton
                                aria-label="delete"
                                onClick={() => {
                                  setSelectedPG(pg);
                                  setOpenConfirmRestorePopUp(true);
                                }}
                                sx={{ padding: "0px" }}
                              >
                                <RestoreIcon sx={iconStyles} />
                              </IconButton>
                            )}
                          </Grid>
                        </TableCell>
                      </TableRow>
                    </>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      // className={classes.tableCell1}
                      colSpan={7}
                      align="center"
                    >
                      <Typography variant="bodySmall" fontWeight={550}>
                        No records found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <Grid container>
            <Paginator
              page={page}
              totalPages={totalPages}
              totalRecord={totalElements}
              onPageChange={handlePageChange}
              onRecordsPerPageChange={handleRecordsPerPageChange}
            />
          </Grid>
        </Grid>

        <ConfirmationPopUp
          open={openConfirmDeletePopUp}
          confirmButtonName="Archive"
          onlyMsg={false}
          onClose={() => setOpenConfirmDeletePopUp(false)}
          onConfirm={() => confirmDelete()}
          message={`Do you really want to archive ${selectedPG?.name || "this PG"} ?`}
          title={`Archive Item`}
          subtitle={"Are you sure you want to archive the following item?"}
          rowData={[
            selectedPG?.name || "",
            `${selectedPG?.address?.line1}, ${selectedPG?.address?.line2}, ${selectedPG?.address?.city}, ${selectedPG?.address?.state}, ${selectedPG?.address?.country}, ${selectedPG?.address?.zipcode}`,
          ]}
          header={[{ header: "Name" }, { header: "Address" }]}
        />
        <ConfirmationPopUp
          open={openConfirmRestorePopUp}
          onClose={() => setOpenConfirmRestorePopUp(false)}
          onConfirm={() => confirmRestore()}
          message={`Do you really want to restore ${selectedPG?.name || "this location"} ?`}
          title={`Restore Item`}
          onlyMsg={false}
          subtitle={"Are you sure you want to restore the following item?"}
          confirmButtonName="Restore"
          rowData={[
            selectedPG?.name || "",
            `${selectedPG?.address?.line1}, ${selectedPG?.address?.line2}, ${selectedPG?.address?.city}, ${selectedPG?.address?.state}, ${selectedPG?.address?.country}, ${selectedPG?.address?.zipcode}`,
          ]}
          header={[{ header: "Name" }, { header: "Address" }]}
        />
      </Grid>
    </Grid>
  );
};

export default ProviderGroupList;
