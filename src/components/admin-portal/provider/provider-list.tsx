import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSearchParams } from "react-router-dom";

import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import { Link, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { AxiosResponse } from "axios";
import { format } from "date-fns";

import CustomInput from "@/common-components/custom-input/custom-input";
import CustomSelectorSq from "@/common-components/custom-selector-sq/custom-selector-sq";
import Paginator from "@/common-components/paginator/paginator";
import Status from "@/common-components/status/status";
import { heading, linkCss, tableCellCss, typographyCss } from "@/common-components/table/common-table-widgets";
import { TableHeaders } from "@/common-components/table/table-models";

import { useDrawer } from "@/components/providers/DrawerProvider";
import DrawerBody from "@/components/ui/DrawerBody";
import ProfileNurse from "@/components/ui/Profile/ProfileNurse";
import { setIsLoading } from "@/redux/actions/loader-action";
import { Provider, ProviderControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { theme } from "@/utils/theme";

export const headers: TableHeaders[] = [
  { header: "Name" },
  { header: "Email" },
  { header: "Address" },
  { header: "Phone Number" },
  { header: "NPI" },
  { header: "License Number" },
  { header: "Licensed State" },
  { header: "License Exp." },
  { header: "Status" },
];

interface ProviderListProps {
  subdomain?: string;
}

const ProviderList = (props: ProviderListProps) => {
  const { subdomain } = props;
  const { open: openDrawer } = useDrawer();
  const [searchParams, setSearchParams] = useSearchParams();
  const xTenantIdVal = subdomain ? GetTenantId(subdomain) : "";
  const [sortBy, setSortBy] = useState("");

  const [sortDirections, setSortDirections] = useState("desc");
  const [sortDirection, setSortDirection] = useState("desc");
  const [sortDirectionByStatus, setSortDirectionByStatus] = useState("desc");

  const dispatch = useDispatch();
  const [selectedFilterOpt, setSelectedFilterOpt] = useState(searchParams.get("role") || "All");

  useEffect(() => {
    setSearchParams((prev) => {
      prev.set("role", selectedFilterOpt);
      return prev;
    });
  }, [selectedFilterOpt, setSearchParams]);

  const [isLoading, setIsLoadingProvider] = useState(false);
  const [getAllProvider, setAllProvider] = useState<Provider[]>([]);

  const [searchQuery, setSearchQuery] = useState("");
  const [searchdebounce, setSearchDebounce] = useState("");

  const handleOnClickLink = (user: Provider) => {
    handleDrawer.viewProviderDetails(user.role === "PROVIDER" ? "View Provider" : "View Nurse", user);
  };

  //Pagination states
  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalElements, setTotalElements] = useState<number>(0);

  const handlePageChange = async (_event: ChangeEvent<unknown> | null, page: number) => {
    setPage(page);
  };

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  //debounec call
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchDebounce(searchQuery);
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, [searchQuery]);

  //getApi
  const getAllProviders = async () => {
    try {
      // setIsLoadingProvider(true)
      const response = await ProviderControllerService.getAllProviders({
        page,
        size,
        sortBy,
        sortDirection,
        role: selectedFilterOpt === "All" ? undefined : selectedFilterOpt === "Nurse" ? "NURSE" : "PROVIDER",
        searchString: searchdebounce,
        xTenantId: xTenantIdVal,
      });
      const listData = response?.data?.content as Provider[];

      const totalElementsFromResponse = (response as unknown as AxiosResponse).data.page.totalElements;

      setAllProvider(listData);
      setTotalElements(totalElementsFromResponse);
      setTotalPages(Math.ceil(totalElementsFromResponse / size));
    } catch (error) {
      error;
    } finally {
      setIsLoadingProvider(false);
    }
  };

  useEffect(() => {
    if (xTenantIdVal) {
      getAllProviders();
    }
  }, [page, size, selectedFilterOpt, searchdebounce, xTenantIdVal, sortBy, sortDirection]);

  useEffect(() => {
    dispatch(setIsLoading(isLoading));
  }, [dispatch, isLoading]);

  const handleSorting = (column: string) => {
    if (column == "Name") {
      setSortBy("firstName");
      setSortDirections((prev) => (prev === "desc" ? "asc" : "desc"));
    } else if (column === "Status") {
      setSortBy("active");
      setSortDirectionByStatus((prev) => (prev === "desc" ? "asc" : "desc"));
    }
  };
  useEffect(() => {
    if (sortBy == "firstName") {
      setSortDirection(sortDirections);
    } else if (sortBy === "active") {
      setSortDirection(sortDirectionByStatus);
    }
  }, [sortBy, sortDirection, handleSorting]);

  const handleDrawer = {
    viewProviderDetails: (title: string, provider: Provider) => {
      openDrawer({
        title: title,
        component: (
          <DrawerBody>
            <ProfileNurse providerId={provider.uuid || ""} xTenantId={xTenantIdVal} />
          </DrawerBody>
        ),
      });
    },
  };

  return (
    <Grid height={"100%"}>
      <Grid
        border={`1px solid ${theme.palette.grey[300]}`}
        boxShadow={`0px 0px 16px 0px #021D2614`}
        height={"100%"}
        borderRadius={"8px"}
        container
        flexDirection={"column"}
      >
        <Grid container p={2} justifyContent={"space-between"}>
          <Grid container alignItems={"center"} columnGap={2}>
            <CustomSelectorSq
              widthOfBtn={"80px"}
              options={["All", "Nurse", "Provider"]}
              onSelect={(selectedOption) => setSelectedFilterOpt(selectedOption)}
              selectedValue={selectedFilterOpt}
            />
          </Grid>
          <Grid container columnGap={2}>
            <Grid>
              <CustomInput
                hasStartSearchIcon
                placeholder={"Search Provider"}
                onChange={(e) => setSearchQuery(e.target.value)}
                name={""}
                value={""}
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Table */}
        <Grid>
          <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
            <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
              <TableHead>
                <TableRow>
                  {headers.map((header, index) => (
                    <TableCell
                      sx={{
                        ...heading,
                        minWidth: header.minWidth ? header.minWidth : "inherit",
                        maxWidth: header.maxWidth ? header.maxWidth : "inherit",
                      }}
                      align="left"
                      key={index}
                    >
                      {header.header === "Name" ? (
                        <Link
                          style={{
                            color: "#667085",
                            textDecoration: "none",
                            cursor: "pointer",
                          }}
                          onClick={() => handleSorting(header.header)}
                        >
                          <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                            {header.header}
                            <Typography mt={0.3}>
                              {sortDirections == "asc" ? (
                                <ArrowUpwardIcon fontSize="small" />
                              ) : (
                                <ArrowDownwardIcon fontSize="small" />
                              )}
                            </Typography>
                          </Typography>
                        </Link>
                      ) : header.header === "Status" ? (
                        <Link
                          style={{
                            color: "#667085",
                            textDecoration: "none",
                            cursor: "pointer",
                          }}
                          onClick={() => handleSorting(header.header)}
                        >
                          <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                            {header.header}
                            <Typography mt={0.3}>
                              {sortDirectionByStatus == "asc" ? (
                                <ArrowUpwardIcon fontSize="small" />
                              ) : (
                                <ArrowDownwardIcon fontSize="small" />
                              )}
                            </Typography>
                          </Typography>
                        </Link>
                      ) : (
                        <Grid
                          container
                          flexDirection={"column"}
                          alignContent={header.header === "Actions" ? `flex-end` : "flex-start"}
                        >
                          <Typography variant="bodySmall">{header.header}</Typography>
                        </Grid>
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {getAllProvider.map((user, index) => (
                  <>
                    <TableRow key={index}>
                      <TableCell sx={{ ...heading }} align="left">
                        <Grid container flexDirection={"column"}>
                          <Link
                            style={{
                              ...linkCss,
                            }}
                            onClick={() => handleOnClickLink(user)}
                          >
                            <Typography fontWeight={550} variant="bodySmall">
                              {`${user.firstName} ${user.lastName}`}
                            </Typography>
                          </Link>
                        </Grid>
                      </TableCell>
                      <TableCell sx={{ ...heading }} align="left">
                        <Grid container flexDirection={"column"}>
                          <Typography sx={typographyCss} variant="bodySmall">
                            {user.email}
                          </Typography>
                        </Grid>
                      </TableCell>
                      <TableCell sx={{ ...heading }} align="left">
                        <Grid container flexDirection={"column"}>
                          <Typography sx={typographyCss} variant="bodySmall">
                            {`${user.address.line1} ${user.address.line2}  ${user.address.city}  ${user.address.state} ${user.address.country}  `}
                          </Typography>
                        </Grid>
                      </TableCell>
                      <TableCell sx={{ ...heading }} align="left">
                        <Grid container flexDirection={"column"}>
                          <Typography sx={typographyCss} variant="bodySmall">
                            {user.phone}
                          </Typography>
                        </Grid>
                      </TableCell>
                      <TableCell sx={{ ...heading }} align="left">
                        <Grid container flexDirection={"column"}>
                          <Typography sx={typographyCss} variant="bodySmall">
                            {user.npi}
                          </Typography>
                        </Grid>
                      </TableCell>

                      {/* License Details (if available) */}
                      {user.providerLicenseDetails?.[0] ? (
                        <>
                          {/* License Number */}
                          <TableCell sx={{ ...heading }} align="left">
                            <Grid container flexDirection={"column"}>
                              <Typography sx={typographyCss} variant="bodySmall">
                                {user.providerLicenseDetails[0].licenseNumber || "-"}
                              </Typography>
                            </Grid>
                          </TableCell>

                          {/* Licensed States */}
                          <TableCell sx={{ ...heading }} align="left">
                            <Grid container flexDirection={"column"}>
                              <Typography sx={typographyCss} variant="bodySmall">
                                {user.providerLicenseDetails?.map((val) => (
                                  <>{val.licensedStates?.map((v) => <>{v.state}, </>)}</>
                                ))}
                              </Typography>
                            </Grid>
                          </TableCell>

                          {/* License Expiry Date */}
                          <TableCell sx={{ ...heading }} align="left">
                            <Grid container flexDirection={"column"}>
                              <Typography sx={typographyCss} variant="bodySmall">
                                {user.providerLicenseDetails[0].expiryDate
                                  ? format(new Date(user.providerLicenseDetails[0].expiryDate), "MM-dd-yyyy")
                                  : "-"}
                              </Typography>
                            </Grid>
                          </TableCell>
                        </>
                      ) : (
                        <>
                          <TableCell sx={{ ...heading }} align="left">
                            <Grid container flexDirection={"column"}>
                              <Typography sx={typographyCss}>-</Typography>
                            </Grid>
                          </TableCell>
                          <TableCell sx={{ ...heading }} align="left">
                            <Grid container flexDirection={"column"}>
                              <Typography sx={typographyCss}>-</Typography>
                            </Grid>
                          </TableCell>
                          <TableCell sx={{ ...heading }} align="left">
                            <Grid container flexDirection={"column"}>
                              <Typography sx={typographyCss}>-</Typography>
                            </Grid>
                          </TableCell>
                        </>
                      )}

                      <TableCell sx={{ ...heading }} align="left">
                        <Grid container flexDirection={"column"} alignContent={`flex-start`}>
                          <Status status={`${user.active ? "ACTIVE" : "INACTIVE"}`} width="74px" />
                        </Grid>
                      </TableCell>
                    </TableRow>
                  </>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <Grid container>
            <Paginator
              page={page}
              totalPages={totalPages}
              totalRecord={totalElements}
              onPageChange={handlePageChange}
              onRecordsPerPageChange={handleRecordsPerPageChange}
            />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default ProviderList;
