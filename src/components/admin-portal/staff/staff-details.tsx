import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

import { Avatar, Typography } from "@mui/material";
import { Box, Grid } from "@mui/system";

import { format } from "date-fns";

import CustomLabel from "@/common-components/custom-label/custom-label";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
import Status from "@/common-components/status/status";

import Logo from "@/assets/image_svg/icons/default-image.svg";
import DrawerBody from "@/components/ui/DrawerBody";
import { Roles } from "@/constants/roles";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { Provider, ProviderControllerService, User, UserControllerService } from "@/sdk/requests";
import { theme } from "@/utils/theme";
import { toCamelCase } from "@/utils/toCamelCase";

type StaffDetailsProps = {
  staffDetails: User;
  role: string;
  nurseDetails: Provider;
  xTenantId: string;
};

const StaffDetails = (props: StaffDetailsProps) => {
  const { staffDetails, role, nurseDetails, xTenantId } = props;
  const dispatch = useDispatch();

  const [avatar, setAvatar] = useState("");

  const getProviderProfile = async () => {
    if (!staffDetails?.uuid) {
      return;
    }
    try {
      const response = UserControllerService.getUser({
        userId: staffDetails.uuid,
        xTenantId: xTenantId,
      });
      if (response) {
        const allData = (await response).data as User;
        setAvatar(allData.avatar || "");
      }
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity).body.message,
        })
      );
    }
  };

  useEffect(() => {
    getProviderProfile();
  }, []);

  const [nurseAvatar, setNurseAvatar] = useState("");

  const getNurseProfile = async () => {
    if (!nurseDetails?.uuid) {
      return;
    }
    try {
      let response = ProviderControllerService.getProviderById({
        providerUuid: nurseDetails.uuid,
        // xTenantId: xTenantId,
      });
      if (response) {
        let allData = (await response).data as User;
        setNurseAvatar(allData.avatar || "");
      }
    } catch (error) {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: (error as ErrorResponseEntity).body.message,
        })
      );
    }
  };

  useEffect(() => {
    getNurseProfile();
  }, []);

  return (
    <DrawerBody padding={3}>
      {role !== "Nurse" && role !== "Provider" && (
        <Grid container flexDirection={"row"} columnGap={3}>
          <Grid display={"flex"} mb={7}>
            {avatar ? (
              <Grid
                display={"flex"}
                flexDirection={"column"}
                gap={2}
                justifyContent={"flex-start"}
                alignItems={"flex-start"}
              >
                <Avatar style={{ width: "80px", height: "80px" }} src={avatar} />
                {/* <Status
                  status={staffDetails?.active ? "ACTIVE" : "INACTIVE"}
                  width="fit-content"
                /> */}
              </Grid>
            ) : (
              <Grid container flexDirection={"column"} gap={2} justifyContent={"flex-start"} alignItems={"flex-start"}>
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                  <Box width={"fit-content"} component={"img"} src={Logo}></Box>
                </Box>
              </Grid>
            )}
          </Grid>
          <Grid flex={1} rowGap={3} container flexDirection={"column"}>
            <Grid flex={1} p={"0px 20px"} container columnGap={2} height={"40px"}>
              <Grid container flexDirection={"column"} rowGap={1} width={"30%"} sx={{ wordBreak: "break-word" }}>
                <Typography variant="bodySmall" color="#515C5F">
                  Name
                </Typography>
                <Typography variant="bodyMedium">
                  {staffDetails.firstName + " " + staffDetails.lastName || "-"}
                </Typography>
              </Grid>

              <Grid container flexDirection={"column"} rowGap={1} width={"30%"} sx={{ wordBreak: "break-word" }} pl={2}>
                <Typography variant="bodySmall" color="#515C5F">
                  Role
                </Typography>
                <Typography variant="bodyMedium">{toCamelCase(staffDetails.role || "") || "-"}</Typography>
              </Grid>

              <Grid container flexDirection={"column"} rowGap={1} width={"30%"} sx={{ wordBreak: "break-word" }} pl={4}>
                <Typography variant="bodySmall" color="#515C5F">
                  Gender
                </Typography>
                <Typography variant="bodyMedium">{toCamelCase(staffDetails.gender || "") || "-"}</Typography>
              </Grid>
            </Grid>
            <Grid flex={1} p={"0px 20px"} container columnGap={2}>
              <Grid container flexDirection={"column"} rowGap={1} width={"30%"} sx={{ wordBreak: "break-word" }}>
                <Typography variant="bodySmall" color="#515C5F">
                  Phone
                </Typography>
                <Typography variant="bodyMedium">{staffDetails.phone || "-"}</Typography>
              </Grid>
              <Grid container flexDirection={"column"} rowGap={1} width={"30%"} sx={{ wordBreak: "break-word" }} pl={2}>
                <Typography variant="bodySmall" color="#515C5F">
                  Email
                </Typography>
                <Typography variant="bodyMedium">{staffDetails.email || "-"}</Typography>
              </Grid>

              {staffDetails.role === Roles.SITE_ADMIN && (
                <Grid
                  container
                  flexDirection={"column"}
                  rowGap={1}
                  width={"30%"}
                  sx={{ wordBreak: "break-word" }}
                  pl={4}
                >
                  <Typography variant="bodySmall" color="#515C5F">
                    Location
                  </Typography>
                  <Typography variant="bodyMedium">{staffDetails?.locationName || "-"}</Typography>
                </Grid>
              )}
            </Grid>
            <Grid flex={1} p={"0px 20px"} container justifyContent={"space-between"}>
              <Grid container flexDirection={"column"} rowGap={1}>
                <Typography variant="bodySmall" color="#515C5F">
                  Address
                </Typography>
                <Typography variant="bodyMedium">
                  {staffDetails.address?.line1
                    ? `${staffDetails?.address?.line1 || "-"},  ${staffDetails?.address?.line2 || "-"},
								 ${staffDetails?.address?.city || "-"}, ${staffDetails?.address?.state || "-"},
								  ${staffDetails?.address?.country || "-"}, ${staffDetails?.address?.zipcode || "-"}`
                    : "-"}
                </Typography>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      )}
      {(role === "Nurse" || role === "Provider") && (
        <Grid container flexDirection={"column"} alignItems={"center"}>
          <Grid display={"flex"} justifyContent={"center"} mb={7}>
            <Grid width={"200px"} display={"flex"} justifyContent={"center"} alignItems={"center"}>
              {nurseAvatar ? (
                <Avatar style={{ width: "80px", height: "80px" }} src={nurseAvatar} />
              ) : (
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                  <Box width={"fit-content"} component={"img"} src={Logo}></Box>
                </Box>
              )}
            </Grid>
          </Grid>
          <Grid flex={1} rowGap={4} container flexDirection={"column"} width={"92%"}>
            <Grid
              flex={1}
              p={"0px 20px"}
              container
              justifyContent={"space-between"}
              gap={2}
              flexDirection={"row"}
              flexWrap={"nowrap"}
            >
              <Grid container flexDirection={"column"} rowGap={1} width={"25%"} whiteSpace={"break-spaces"}>
                <Typography variant="bodySmall" color="#515C5F">
                  Name
                </Typography>
                <Typography variant="bodyMedium">
                  {nurseDetails?.firstName + " " + nurseDetails?.lastName || "-"}
                </Typography>
              </Grid>

              <Grid container flexDirection={"column"} rowGap={1} width={"25%"} whiteSpace={"break-spaces"}>
                <Typography variant="bodySmall" color="#515C5F">
                  Role
                </Typography>
                <Typography variant="bodyMedium">{toCamelCase(nurseDetails?.role || "") || "-"}</Typography>
              </Grid>
              <Grid container flexDirection={"column"} rowGap={1} width={"25%"} whiteSpace={"break-spaces"}>
                <Typography variant="bodySmall" color="#515C5F">
                  Status
                </Typography>
                <Status status={nurseDetails?.active ? "ACTIVE" : "INACTIVE"} width="fit-content" />
              </Grid>
              <Grid container flexDirection={"column"} rowGap={1} width={"25%"} whiteSpace={"break-spaces"}>
                <Typography variant="bodySmall" color="#515C5F">
                  Gender
                </Typography>
                <Typography variant="bodyMedium">{toCamelCase(nurseDetails?.gender || "") || "-"}</Typography>
              </Grid>
            </Grid>
            <Grid flex={1} p={"0px 20px"} container gap={2}>
              <Grid container flexDirection={"column"} rowGap={1} width={"23%"} sx={{ wordBreak: "break-word" }}>
                <Typography variant="bodySmall" color="#515C5F">
                  Phone
                </Typography>
                <Typography variant="bodyMedium">{nurseDetails?.phone || "-"}</Typography>
              </Grid>

              <Grid container flexDirection={"column"} sx={{ wordBreak: "break-word" }} rowGap={1} width={"49%"}>
                <Typography variant="bodySmall" color="#515C5F">
                  Email
                </Typography>
                <Typography variant="bodyMedium">
                  {nurseDetails?.email || "-"}
                  {/* {"<EMAIL>"} */}
                </Typography>
              </Grid>
              <Grid container flexDirection={"column"} rowGap={1}>
                <Typography variant="bodySmall" color="#515C5F">
                  NPI
                </Typography>
                <Typography variant="bodyMedium">{nurseDetails?.npi || "-"}</Typography>
              </Grid>
            </Grid>
            <Grid flex={1} p={"0px 20px"} container justifyContent={"space-between"}>
              <Grid container flexDirection={"column"} rowGap={1}>
                <Typography variant="bodySmall" color="#515C5F">
                  Address
                </Typography>
                <Typography variant="bodyMedium">
                  {nurseDetails.address?.line1
                    ? `${nurseDetails?.address?.line1 || "-"},  ${nurseDetails?.address?.line2 || "-"},
								 ${nurseDetails?.address?.city || "-"}, ${nurseDetails?.address?.state || "-"},
								  ${nurseDetails?.address?.country || "-"}, ${nurseDetails?.address?.zipcode || "-"}`
                    : "-"}
                </Typography>
              </Grid>
            </Grid>
          </Grid>
          {/* nurse license information */}
          <Grid marginTop={7} width={"92%"}>
            <Grid container flexDirection={"column"}>
              <Grid mb={1}>
                <Typography variant="bodyMedium" fontWeight={550}>
                  {"License Details"}
                </Typography>
              </Grid>
              <Grid
                p={1}
                bgcolor={theme.palette.secondary.light}
                container
                flexDirection={"column"}
                justifyContent={"space-between"}
                rowGap={0.5}
                padding={2}
                borderRadius={2}
              >
                {nurseDetails.providerLicenseDetails && nurseDetails.providerLicenseDetails?.length > 0 ? (
                  nurseDetails.providerLicenseDetails?.map((license) => (
                    <Grid container key={license.uuid} justifyContent={"space-between"}>
                      <Grid width={"29%"}>
                        <CustomLabel label="Licensed Number" />
                        <Typography>{license.licenseNumber || "-"}</Typography>
                      </Grid>
                      <Grid width={"29%"}>
                        <CustomLabel label="Licensed State" />
                        <Typography>
                          {license?.licensedStates?.map((state, index) => (
                            <span key={index}>{state.state || "-"}</span>
                          ))}
                        </Typography>
                      </Grid>
                      <Grid width={"29%"}>
                        <CustomLabel label="License Expiry Date" />
                        <Typography>
                          {(license?.expiryDate && format(new Date(license?.expiryDate), "MM-dd-yyyy")) || "-"}
                        </Typography>
                      </Grid>
                    </Grid>
                  ))
                ) : (
                  <Grid container justifyContent={"space-between"}>
                    <Grid width={"29%"}>
                      <CustomLabel label="Licensed Number" />
                      <Typography>{"-"}</Typography>
                    </Grid>
                    <Grid width={"29%"}>
                      <CustomLabel label="Licensed State" />
                      <Typography>{"-"}</Typography>
                    </Grid>
                    <Grid width={"29%"}>
                      <CustomLabel label="License Expiry Date" />
                      <Typography>{"-"}</Typography>
                    </Grid>
                  </Grid>
                )}
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      )}
    </DrawerBody>
  );
};

export default StaffDetails;
