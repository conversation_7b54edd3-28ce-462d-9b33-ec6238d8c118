import { ChangeEvent, useEffect, useState } from "react";

import AddIcon from "@mui/icons-material/Add";
import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import PlayCircleOutlineRoundedIcon from "@mui/icons-material/PlayCircleOutlineRounded";
import RestoreIcon from "@mui/icons-material/Restore";
import {
  Box,
  Button,
  IconButton,
  Skeleton,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomAutoComplete from "@/common-components/custom-auto-complete/custom-auto-complete";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomSelectorSq from "@/common-components/custom-selector-sq/custom-selector-sq";
import Paginator from "@/common-components/paginator/paginator";
import { heading, tableCellCss } from "@/common-components/table/common-table-widgets";

import { useDrawer } from "@/components/providers/DrawerProvider";
import useApiFeedback from "@/hooks/useApiFeedback";
import { ContentObject } from "@/models/response/response-content-entity";
import {
  useDeviceControllerServiceUpdateDeviceArchiveStatus,
  useDeviceControllerServiceUpdateDeviceStatus,
} from "@/sdk/queries";
import { Device, DeviceControllerService, MedicalCode, MedicalCodeControllerService } from "@/sdk/requests";
import { formatNewDateFormat } from "@/utils/format/date";

import DevicesForm from "./devicesForm";

// headers
const headerName = ["Code", "Description"];
const DevicesHeaderName = ["Name", "Type", "Created By", "Created On", "Description", "Status", "Action"];

// Device popup table headers
const devicePopupHeaders = [{ header: "Name" }, { header: "Type" }, { header: "Description" }];

function DrugLibraryTab() {
  const { open: openDrawer } = useDrawer();
  const [searchDrug, setSearchDrug] = useState("");
  const [medicalCodes, setMedicalCodes] = useState<MedicalCode[]>([]);
  const [medicalCodeType, setMedicalCodeType] = useState(() => {
    const savedType = localStorage.getItem("medicalCodeType");
    return savedType || "ICD-10";
  });
  // Pagination state
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState<number>(0);
  const [size, setSize] = useState(10);
  const [devices, setDevices] = useState<Device[]>([]);

  // Archive/Restore state
  const [selectedDevice, setSelectedDevice] = useState<Device>();
  const [openConfirmDeletePopUp, setOpenConfirmDeletePopUp] = useState(false);
  const [openConfirmRestorePopUp, setOpenConfirmRestorePopUp] = useState(false);
  // Add state for activation/deactivation confirmation dialogs
  const [openConfirmActivatePopUp, setOpenConfirmActivatePopUp] = useState(false);
  const [openConfirmDeactivatePopUp, setOpenConfirmDeactivatePopUp] = useState(false);
  const xTenantId = "eamata";

  // filter state
  const [filterDeviceType, setFilterDeviceType] = useState("");

  // CSS styles
  const typographyCss = {
    fontFamily: "Roboto",
    fontWeight: 400,
    fontSize: "14px",
    lineHeight: "20px",
    letterSpacing: "0%",
    color: "#212D30",
  };

  // API call to get medical codes
  const { data, isLoading, isSuccess, refetch, isRefetching } = useQuery({
    queryKey: ["medical-codes", page, size, searchDrug, medicalCodeType, filterDeviceType],
    queryFn: () => {
      if (medicalCodeType === "Devices") {
        return DeviceControllerService.getAllDevices({
          xTenantId: "eamata",
          page,
          size,
          sortBy: "created",
          sortDirection: "desc",
          // status: true,
          searchString: searchDrug,
          type: filterDeviceType == "" ? undefined : filterDeviceType,
        });
      } else {
        return MedicalCodeControllerService.getMedicalCodes({
          xTenantId: "eAmata",
          page,
          size,
          searchString: searchDrug,
          type: medicalCodeType === "ICD-10" ? "ICD10" : (medicalCodeType as "ICD10" | "CPT"),
        });
      }
    },
  });

  useEffect(() => {
    if (isSuccess && data) {
      const apiResponse = (data as unknown as AxiosResponse).data;

      if (medicalCodeType === "Devices") {
        let devices = [];
        let pageInfo = { totalPages: 0, totalElements: 0 };

        if (apiResponse?.data?.content) {
          devices = apiResponse.data.content;
          pageInfo = apiResponse.data.page || pageInfo;
        } else if (apiResponse?.content) {
          devices = apiResponse.content;
          pageInfo = apiResponse.page || pageInfo;
        } else if (Array.isArray(apiResponse)) {
          devices = apiResponse;
        } else if (Array.isArray(apiResponse?.data)) {
          devices = apiResponse.data;
        }

        setDevices(devices || []);
        setTotalPages(pageInfo.totalPages || 0);
        setTotalElements(pageInfo.totalElements || 0);
      } else {
        const contentObject = apiResponse as ContentObject<MedicalCode[]>;
        const medicalCodesData = contentObject?.content;
        setTotalPages((contentObject?.page?.totalPages as number) || 0);
        setTotalElements((contentObject?.page?.totalElements as number) || 0);

        // Map only code and description from the response
        const mappedData = medicalCodesData?.map((code: MedicalCode) => ({
          uuid: code.uuid,
          code: code.code,
          description: code.description || "",
          type: code.type,
          active: code.active,
          archive: code.archive,
          errorMessage: code.errorMessage,
        }));

        setMedicalCodes(mappedData || []);
      }
    }
  }, [data, isSuccess, medicalCodeType]);

  useEffect(() => {
    // Clear search when switching between types
    setSearchDrug("");
    refetch();
  }, [medicalCodeType, refetch]);

  // Save medicalCodeType to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("medicalCodeType", medicalCodeType);
  }, [medicalCodeType]);

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handlePageChange = (_event: ChangeEvent<unknown> | null, newPage: number) => {
    setPage(newPage);
  };

  const handleDrawerActions = {
    addDevice: () => {
      openDrawer({
        title: "Add New Device",
        component: <DevicesForm />,
      });
    },
    editDevice: (device: Device) => {
      openDrawer({
        title: "Edit Device",
        component: <DevicesForm device={device} />,
      });
    },
  };

  // Archive/Restore mutation
  const {
    mutateAsync: mutateAsyncArchive,
    isError: isErrorArchive,
    error: errorArchive,
    isSuccess: isSuccessArchive,
    data: dataArchive,
  } = useDeviceControllerServiceUpdateDeviceArchiveStatus();

  // Device status update mutation
  const {
    mutateAsync: updateDeviceStatus,
    isError: isErrorStatus,
    error: errorStatus,
    isSuccess: isSuccessStatus,
    data: dataStatus,
  } = useDeviceControllerServiceUpdateDeviceStatus();

  const confirmDelete = async () => {
    await mutateAsyncArchive({
      deviceUuid: selectedDevice?.uuid || "",
      status: true,
      xTenantId: xTenantId,
    });
    await refetch();
    setOpenConfirmDeletePopUp(false);
  };

  const confirmRestore = async () => {
    await mutateAsyncArchive({
      deviceUuid: selectedDevice?.uuid || "",
      status: false,
      xTenantId: xTenantId,
    });
    await refetch();
    setOpenConfirmRestorePopUp(false);
  };

  useApiFeedback(
    isErrorArchive,
    errorArchive,
    isSuccessArchive,
    (dataArchive?.message || "Device archive status updated!") as string
  );

  // Add API feedback for device status update
  useApiFeedback(
    isErrorStatus,
    errorStatus,
    isSuccessStatus,
    (dataStatus?.message || "Device status updated!") as string
  );

  // Create row data for popup when archiving/restoring
  const getPopupRowData = (device: Device) => {
    return [device.name || "-", device.deviceType || "-", device.description || "-"];
  };

  // Handle device status toggle
  const handleStatusToggle = async (device: Device) => {
    setSelectedDevice(device);
    if (device.active) {
      setOpenConfirmDeactivatePopUp(true);
    } else {
      setOpenConfirmActivatePopUp(true);
    }
  };

  // Confirm device activation
  const confirmActivate = async () => {
    await updateDeviceStatus({
      deviceUuid: selectedDevice?.uuid || "",
      status: true,
      xTenantId: xTenantId,
    });
    await refetch();
    setOpenConfirmActivatePopUp(false);
  };

  // Confirm device deactivation
  const confirmDeactivate = async () => {
    await updateDeviceStatus({
      deviceUuid: selectedDevice?.uuid || "",
      status: false,
      xTenantId: xTenantId,
    });
    await refetch();
    setOpenConfirmDeactivatePopUp(false);
  };

  return (
    <Grid border={"1px solid #EAECF0"} borderRadius={2}>
      <Grid container justifyContent={"space-between"} mb={1} px={2} pt={2}>
        <Grid container alignItems={"center"} gap={2} display={"flex"} flexDirection={"row"}>
          <Grid>
            <Grid container direction="column" gap={1}>
              <CustomSelectorSq
                widthOfBtn="100px"
                options={["ICD-10", "CPT", "Devices"]}
                onSelect={(selectedOption: string) => {
                  setPage(0);
                  setMedicalCodeType(selectedOption);
                }}
                selectedValue={medicalCodeType}
              />
            </Grid>
          </Grid>
        </Grid>
        <Grid container alignItems={"center"} gap={2} display={"flex"} flexDirection={"row"}>
          {medicalCodeType == "Devices" && (
            <>
              <Grid size={4} direction="column" sx={{ width: "300px" }}>
                <CustomAutoComplete
                  placeholder="Filter Device Type"
                  options={[
                    { key: "RPM", value: "RPM" },
                    { key: "infusion", value: "infusion" },
                    { key: "Elastomeric Infusion Pump", value: "Elastomeric Infusion Pump" },
                    { key: "Catheter Securement Device", value: "Catheter Securement Device" },
                    { key: "Blood Pressure Monitor", value: "Blood Pressure Monitor" },
                    { key: "Glucose Monitor", value: "Glucose Monitor" },
                  ]}
                  value={filterDeviceType}
                  onChange={(newValue: string) => {
                    setFilterDeviceType(newValue);
                  }}
                />
              </Grid>
            </>
          )}

          <Grid>
            <CustomInput
              placeholder={medicalCodeType === "Devices" ? "Search Devices" : `Search ${medicalCodeType} Code`}
              name="code"
              hasStartSearchIcon={true}
              value={searchDrug}
              onDebounceCall={(searchString) => {
                setSearchDrug(searchString);
                setPage(0);
              }}
              onInputEmpty={() => setSearchDrug("")}
            />
          </Grid>
          {medicalCodeType == "Devices" ? (
            <Grid>
              <Button
                startIcon={<AddIcon />}
                variant="contained"
                sx={{ borderRadius: "8px" }}
                onClick={handleDrawerActions.addDevice}
              >
                Add Device
              </Button>
            </Grid>
          ) : (
            ""
          )}
        </Grid>
      </Grid>
      <Grid>
        {medicalCodeType == "Devices" ? (
          <>
            <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
              <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
                <TableHead>
                  <TableRow>
                    {DevicesHeaderName.map((header, index) => (
                      <TableCell
                        sx={{
                          ...heading,
                          minWidth: "150px",
                          width: "auto",
                        }}
                        align="left"
                        key={index}
                      >
                        <Typography fontWeight={550} variant="bodySmall" color="#667085" sx={{ fontStyle: "Roboto" }}>
                          {header}
                        </Typography>
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {isLoading || isRefetching ? (
                    [...Array(5)].map((_, index) => (
                      <TableRow key={index}>
                        {[...Array(DevicesHeaderName.length)].map((_, cellIndex) => (
                          <TableCell key={cellIndex}>
                            <Skeleton variant="text" width={100} />
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : devices.length > 0 ? (
                    devices.map((device, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Typography sx={typographyCss}>{device.name}</Typography>
                        </TableCell>
                        <TableCell>
                          <Typography sx={typographyCss}>{device.deviceType}</Typography>
                        </TableCell>
                        <TableCell>
                          <Typography sx={typographyCss}>{device?.createdBy || "-"}</Typography>
                        </TableCell>
                        <TableCell>
                          <Typography sx={typographyCss}>{formatNewDateFormat(device?.created)}</Typography>
                        </TableCell>
                        <TableCell>
                          <Typography sx={typographyCss}>{device.description}</Typography>
                        </TableCell>
                        <TableCell>
                          <Switch
                            checked={device.active || false}
                            size="small"
                            onChange={() => handleStatusToggle(device)}
                            sx={{
                              width: 42,
                              height: 26,
                              padding: 0,
                              "& .MuiSwitch-switchBase": {
                                padding: 0,
                                margin: "2px",
                                transitionDuration: "300ms",
                                "&.Mui-checked": {
                                  transform: "translateX(16px)",
                                  color: "#fff",
                                  "& + .MuiSwitch-track": {
                                    backgroundColor: "#4CAF50",
                                    opacity: 1,
                                    border: 0,
                                  },
                                },
                              },
                              "& .MuiSwitch-thumb": {
                                boxSizing: "border-box",
                                width: 22,
                                height: 22,
                              },
                              "& .MuiSwitch-track": {
                                borderRadius: 26 / 2,
                                backgroundColor: "#E9E9EA",
                                opacity: 1,
                              },
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            {device.guideLink && (
                              <Tooltip title="View Guide">
                                <IconButton
                                  size="small"
                                  component="a"
                                  href={device.guideLink}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  <PlayCircleOutlineRoundedIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            <Tooltip title="Edit">
                              <IconButton
                                size="small"
                                onClick={() => {
                                  handleDrawerActions.editDevice(device);
                                }}
                              >
                                <EditOutlinedIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            {!device.archive ? (
                              <Tooltip title="Archive">
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    setSelectedDevice(device);
                                    setOpenConfirmDeletePopUp(true);
                                  }}
                                >
                                  <ArchiveOutlinedIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            ) : (
                              <Tooltip title="Restore">
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    setSelectedDevice(device);
                                    setOpenConfirmRestorePopUp(true);
                                  }}
                                >
                                  <RestoreIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={DevicesHeaderName.length} align="center">
                        <Typography>No Devices found</Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        ) : (
          <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
            <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
              <TableHead>
                <TableRow>
                  {headerName.map((header, index) => (
                    <TableCell
                      sx={{
                        ...heading,
                      }}
                      align="left"
                      key={index}
                    >
                      <Typography fontWeight={550} variant="bodySmall" color="#667085" sx={{ fontStyle: "Roboto" }}>
                        {header}
                      </Typography>
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading || isRefetching ? (
                  [...Array(5)].map((_, index) => (
                    <TableRow key={index}>
                      {[...Array(2)].map((_, cellIndex) => (
                        <TableCell key={cellIndex}>
                          <Skeleton variant="text" width={100} />
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : medicalCodes.length > 0 ? (
                  medicalCodes.map((code, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Typography sx={typographyCss}>{code.code}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography sx={typographyCss}>{code.description}</Typography>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={2} align="center">
                      <Typography>No {medicalCodeType} codes found</Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        <Grid container justifyContent={"flex-end"} p={2}>
          <Paginator
            page={page}
            totalPages={totalPages}
            totalRecord={totalElements}
            onPageChange={handlePageChange}
            onRecordsPerPageChange={handleRecordsPerPageChange}
          />
        </Grid>

        {/* Archive Confirmation Dialog */}
        <ConfirmationPopUp
          open={openConfirmDeletePopUp}
          confirmButtonName="Archive"
          onClose={() => setOpenConfirmDeletePopUp(false)}
          onConfirm={() => confirmDelete()}
          message={`Do you really want to archive this Device?`}
          title={`Archive Item`}
          subtitle={"Are you sure you want to archive the following device?"}
          onlyMsg={false}
          header={devicePopupHeaders}
          rowData={selectedDevice ? getPopupRowData(selectedDevice) : []}
        />

        {/* Restore Confirmation Dialog */}
        <ConfirmationPopUp
          open={openConfirmRestorePopUp}
          onClose={() => setOpenConfirmRestorePopUp(false)}
          onConfirm={() => confirmRestore()}
          message={`Do you really want to restore this Device?`}
          title={`Restore Item`}
          subtitle={"Are you sure you want to restore the following device?"}
          confirmButtonName="Restore"
          onlyMsg={false}
          header={devicePopupHeaders}
          rowData={selectedDevice ? getPopupRowData(selectedDevice) : []}
        />

        {/* Activate Confirmation Dialog */}
        <ConfirmationPopUp
          open={openConfirmActivatePopUp}
          onClose={() => setOpenConfirmActivatePopUp(false)}
          onConfirm={() => confirmActivate()}
          message={`Do you really want to activate this Device?`}
          title={`Activate Device`}
          subtitle={"Are you sure you want to activate the following device?"}
          confirmButtonName="Activate"
          onlyMsg={false}
          header={devicePopupHeaders}
          rowData={selectedDevice ? getPopupRowData(selectedDevice) : []}
        />

        {/* Deactivate Confirmation Dialog */}
        <ConfirmationPopUp
          open={openConfirmDeactivatePopUp}
          onClose={() => setOpenConfirmDeactivatePopUp(false)}
          onConfirm={() => confirmDeactivate()}
          message={`Do you really want to deactivate this Device?`}
          title={`Inactive Device`}
          subtitle={"Are you sure you want to deactivate the following device?"}
          confirmButtonName="Inactive"
          onlyMsg={false}
          header={devicePopupHeaders}
          rowData={selectedDevice ? getPopupRowData(selectedDevice) : []}
        />
      </Grid>
    </Grid>
  );
}

export default DrugLibraryTab;
