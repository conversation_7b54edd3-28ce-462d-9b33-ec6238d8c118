import { useRef, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import { Box, Button, Grid2 as Grid, Typography } from "@mui/material";

import { yupResolver } from "@hookform/resolvers/yup";
import { useQueryClient } from "@tanstack/react-query";
import * as yup from "yup";

import CustomInput from "@/common-components/custom-input/custom-input";
import CustomLabel from "@/common-components/custom-label/custom-label";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import { useDrawer } from "@/components/providers/DrawerProvider";
// import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
// import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import DrawerFooter from "@/components/ui/DrawerFooter";
import { Input } from "@/components/ui/Form/Input";
import Select from "@/components/ui/Form/Select";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { Device, DeviceControllerService } from "@/sdk/requests";

interface DeviceFormValues {
  name: string;
  type: string;
  status?: string;
  description: string;
  tutorialLink?: string;
}

const schema = yup.object().shape({
  name: yup.string().required("Device Name is required"),
  type: yup.string().required("Type is required"),
  status: yup.string().when("$isEditMode", {
    is: true,
    then: () => yup.string().required("Status is required"),
    otherwise: () => yup.string(),
  }),
  description: yup.string().required("Description is required"),
  tutorialLink: yup.string(),
});

type DeviceFormProps = {
  device?: Device;
};

const DevicesForm = ({ device }: DeviceFormProps) => {
  const { close: closeDrawer } = useDrawer();
  const formRef = useRef<HTMLFormElement>(null);
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditMode = !!device?.uuid;

  const methods = useForm<DeviceFormValues>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: device?.name || "",
      type: device?.deviceType || "",
      status: device?.active ? "Active" : "Inactive",
      description: device?.description || "",
      tutorialLink: device?.guideLink || "",
    },
    context: { isEditMode },
  });

  const onSubmit = async (data: DeviceFormValues) => {
    try {
      setIsSubmitting(true);

      const deviceData: Device = {
        name: data.name,
        deviceType: data.type,
        description: data.description,
        guideLink: data.tutorialLink,
        active: isEditMode ? data.status === "Active" : true, // Always set active to true for new devices
        archive: false,
      };

      if (isEditMode) {
        // Update existing device
        await DeviceControllerService.updateDevice({
          xTenantId: "eAmata",
          requestBody: {
            ...deviceData,
            uuid: device.uuid,
          },
        });

        // Show success message
        dispatch(
          setSnackbarOn({
            severity: AlertSeverity.SUCCESS,
            message: "Device updated successfully",
          })
        );
      } else {
        // Create new device
        await DeviceControllerService.createDevice({
          xTenantId: "eAmata",
          requestBody: deviceData,
        });

        // Show success message
        dispatch(
          setSnackbarOn({
            severity: AlertSeverity.SUCCESS,
            message: "Device added successfully",
          })
        );
      }

      // Invalidate and refetch devices query
      queryClient.invalidateQueries({
        queryKey: ["medical-codes"],
      });
      closeDrawer();
    } catch (error) {
      // Show error message
      const errorMessage =
        (error as ErrorResponseEntity)?.body?.message || (error instanceof Error ? error.message : "An error occurred");

      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: errorMessage,
        })
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitButtonClick = () => {
    if (formRef.current) {
      formRef.current.dispatchEvent(new Event("submit", { cancelable: true, bubbles: true }));
    }
  };

  const {
    formState: { errors },
  } = methods;

  return (
    <FormProvider {...methods}>
      <Box sx={{ height: "calc(100% - 80px)", overflow: "auto" }}>
        <form ref={formRef} onSubmit={methods.handleSubmit(onSubmit)}>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2, p: 3 }}>
            <Grid container spacing={2}>
              <Grid size={6}>
                <Input name="name" label="Device Name" isRequired />
              </Grid>
              <Grid size={6}>
                <Select
                  name="type"
                  label="Type"
                  isRequired
                  options={[
                    { label: "RPM", value: "RPM" },
                    { label: "Elastomeric Infusion Pump", value: "Elastomeric Infusion Pump" },
                    { label: "Catheter Securement Device", value: "Catheter Securement Device" },
                    { label: "Blood Pressure Monitor", value: "Blood Pressure Monitor" },
                    { label: "Glucose Monitor", value: "Glucose Monitor" },
                  ]}
                />
              </Grid>
              {isEditMode && (
                <Grid size={6}>
                  <Select
                    name="status"
                    label="Status"
                    isRequired
                    options={[
                      { label: "Active", value: "Active" },
                      { label: "Inactive", value: "Inactive" },
                    ]}
                  />
                </Grid>
              )}
              <Grid size={12}>
                <CustomLabel label="Description" isRequired />
                <CustomInput
                  multiline
                  rows={4}
                  name="description"
                  placeholder="Enter Description"
                  value={methods.getValues("description")}
                  hasError={!!errors.description}
                  errorMessage={errors.description?.message}
                  onChange={(e) => methods.setValue("description", e.target.value)}
                />
              </Grid>
              <Grid size={12}>
                <Input
                  name="tutorialLink"
                  label="Device Tutorial Link"
                  placeholder="Enter YouTube or other tutorial link"
                />
              </Grid>
            </Grid>
          </Box>
        </form>
      </Box>
      <DrawerFooter>
        <Button
          onClick={handleSubmitButtonClick}
          variant="contained"
          disabled={isSubmitting}
          sx={{
            borderRadius: "8px",
            textTransform: "none",
            backgroundColor: "#006D8F",
            "&:hover": {
              backgroundColor: "#005F56",
            },
          }}
        >
          <Typography variant="bodySmall">
            {isSubmitting ? "Saving..." : isEditMode ? "Update Device" : "Add Device"}
          </Typography>
        </Button>
      </DrawerFooter>
    </FormProvider>
  );
};

export default DevicesForm;
