import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSearchParams } from "react-router-dom";

import AddIcon from "@mui/icons-material/Add";
import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import RestoreIcon from "@mui/icons-material/Restore";
import {
  Button,
  IconButton,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { format } from "date-fns";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomSelectorSq from "@/common-components/custom-selector-sq/custom-selector-sq";
import Paginator from "@/common-components/paginator/paginator";
import Status from "@/common-components/status/status";
import {
  heading,
  iconStyles,
  linkCss,
  tableCellCss,
  typographyCss,
} from "@/common-components/table/common-table-widgets";
import { TableHeaders } from "@/common-components/table/table-models";

import { useDrawer } from "@/components/providers/DrawerProvider";
import DrawerBody from "@/components/ui/DrawerBody";
import ProfileNurse from "@/components/ui/Profile/ProfileNurse";
import ProfileStaff from "@/components/ui/Profile/ProfileStaff";
import { Roles, RolesOfAdminUsers, RolesOfPGUsers } from "@/constants/roles";
import useApiFeedback from "@/hooks/useApiFeedback";
import { ContentObject } from "@/models/response/response-content-entity";
import { setIsLoading } from "@/redux/actions/loader-action";
import {
  useProviderControllerServiceUpdateProviderArchiveStatus,
  useUserControllerServiceUpdateUserArchiveStatus,
} from "@/sdk/queries";
import { Provider, ProviderControllerService, User, UserControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { theme } from "@/utils/theme";

import NurseForm from "../nurse/nurse-form";
import StaffForm from "./staff-form";

export const headers: TableHeaders[] = [
  { header: "Name" },
  { header: "Email" },
  { header: "Address" },
  { header: "Phone Number" },
  { header: "Status" },
  { header: "Actions" },
];

export const headersNurse: TableHeaders[] = [
  { header: "Name" },
  { header: "Email" },
  { header: "Address" },
  { header: "Phone Number" },
  { header: "NPI" },
  { header: "Licensed State" },
  { header: "License Number" },
  { header: "License Exp." },
  { header: "Status" },
  { header: "Actions" },
];
type StaffListProps = {
  subdomain: string;
  filterOptions: string[];
  roleType: string;
  listType: string;

  onChangeFilter?: (option: string) => void;
  isPGActive?: boolean;
};

const StaffList = (props: StaffListProps) => {
  const { subdomain, isPGActive, filterOptions, roleType, listType, onChangeFilter } = props;
  const { open: openDrawer, close: closeDrawer } = useDrawer();
  const [searchParams, setSearchParams] = useSearchParams();

  const isAdminUser = listType === "ADMIN";
  const [headersArr, setHeaderArr] = useState(headers);
  const xTenantIdVal = isAdminUser ? "" : subdomain ? GetTenantId(subdomain) : "";
  const [totalPages, setTotalPages] = useState(0);
  const [selectedStaff, setSelectedStaff] = useState<User>();
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [openWarningPopUp, setOpenWarningPopUp] = useState(false);

  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);
  const [searchStrings, setSearchstring] = useState<string>("");
  const [sortBy, setSortBy] = useState("");
  const [sortDirection, setSortDirection] = useState("desc");

  const [rows, setRows] = useState<User[]>([]);
  const [rowsNurse, setRowsNurse] = useState<Provider[]>([]);
  const [totalElements, setTotalElements] = useState<number>(0);
  const dispatch = useDispatch();
  const [openConfirmDeletePopUp, setOpenConfirmDeletePopUp] = useState(false);
  const [openConfirmRestorePopUp, setOpenConfirmRestorePopUp] = useState(false);
  const [status] = useState<boolean | undefined>(undefined);
  const [archive] = useState<boolean | undefined>(undefined);

  const [selectedFilterOpt, setSelectedFilterOpt] = useState(searchParams.get("user") || filterOptions[0]);

  useEffect(() => {
    if (selectedFilterOpt) {
      setSearchParams((prev) => {
        prev.set("user", selectedFilterOpt);
        return prev;
      });
    }
  }, [selectedFilterOpt, setSearchParams]);

  useEffect(() => {
    if (selectedFilterOpt === "Nurse") {
      setHeaderArr(headersNurse);
    } else {
      setHeaderArr(headers);
    }

    onChangeFilter && onChangeFilter(selectedFilterOpt);
  }, [selectedFilterOpt]);

  const getRole = (role: string) => {
    if (role === "Front Desk") {
      return Roles.FRONTDESK;
    } else if (role === "Biller") {
      return Roles.BILLER;
    } else if (role === "Provider Group Admin") {
      return Roles.PROVIDER_GROUP_ADMIN;
    } else if (role === "Site Admin") {
      return Roles.SITE_ADMIN;
    } else if (role === "Super Admin") {
      return Roles.SUPER_ADMIN;
    } else if (role === "Staff") {
      return Roles.STAFF;
    } else if (role === "Nurse") {
      return Roles.NURSE;
    }
    return Roles.BILLER;
  };

  const { data, isLoading, isSuccess, refetch } = useQuery({
    queryKey: ["list-of-staff", page, size, searchStrings, selectedFilterOpt, sortDirection, sortBy, xTenantIdVal],
    queryFn: () =>
      UserControllerService.getAllUsers({
        page,
        size,
        sortBy,
        sortDirection,
        archive,
        role: getRole(selectedFilterOpt),
        roleType:
          selectedFilterOpt === "Nurse" ? "PROVIDER" : (roleType as "PATIENT" | "STAFF" | "PROVIDER") || "STAFF",
        searchString: searchStrings,
        status,
        xTenantId: xTenantIdVal,
      }),
    enabled: selectedFilterOpt !== "Nurse" && (isAdminUser || !!xTenantIdVal),
  });

  const {
    data: dataGetProvider,
    // isPending: isPendingGetProvider,
    // isLoading: isLoadingGetProvider,
    isSuccess: isSuccessGetProvider,
    refetch: refetchGetProvider,
  } = useQuery({
    queryKey: ["list-of-staff", page, size, searchStrings, selectedFilterOpt, sortBy, sortDirection],
    queryFn: () =>
      ProviderControllerService.getAllProviders({
        page,
        size,
        sortBy,
        sortDirection,
        role: getRole(selectedFilterOpt) as
          | "SUPER_ADMIN"
          | "FRONTDESK"
          | "BILLER"
          | "NURSE"
          | "PATIENT"
          | "ANONYMOUS"
          | "PROVIDER_GROUP_ADMIN"
          | "SITE_ADMIN"
          | "PROVIDER",
        searchString: searchStrings,
      }),
    enabled: selectedFilterOpt === "Nurse",
  });

  useEffect(() => {
    if (isSuccess) {
      const response = (data as unknown as AxiosResponse).data as ContentObject<User[]>;

      const userData = response?.content;
      setTotalPages(response?.page?.totalPages as number);
      setTotalElements(response?.page?.totalElements as number);

      const tablePayload = userData?.map((user) => {
        return {
          uuid: user?.uuid,
          firstName: user.firstName,
          lastName: user.lastName,
          address: {
            line1: user.address?.line1,
            line2: user.address?.line2,
            city: user.address?.city,
            state: user.address?.state,
            country: user.address?.country,
            zipcode: user.address?.zipcode,
          },

          phone: user?.phone,
          active: user?.active,
          email: user?.email,
          archive: user.archive,
          role: user?.role,
          gender: user.gender,
          locationId: user?.locationId,
          locationName: user?.locationName,
          emailVerified: user?.emailVerified,
        } as User;
      });

      setRows(tablePayload);
    }
  }, [data, isSuccess]);

  useEffect(() => {
    if (isSuccessGetProvider) {
      const response = (dataGetProvider as unknown as AxiosResponse).data as ContentObject<Provider[]>;

      const userData = response?.content;
      setTotalPages(response?.page?.totalPages as number);
      setTotalElements(response?.page?.totalElements as number);

      const tablePayload = userData?.map((user) => {
        return {
          uuid: user?.uuid,
          firstName: user.firstName,
          lastName: user.lastName,
          address: {
            line1: user.address?.line1,
            line2: user.address?.line2,
            city: user.address?.city,
            state: user.address?.state,
            country: user.address?.country,
            zipcode: user.address?.zipcode,
          },
          phone: user?.phone,
          active: user?.active,
          email: user?.email,
          archive: user.archive,
          role: user?.role,
          gender: user.gender,
          npi: user.npi,
          providerLicenseDetails: user.providerLicenseDetails,
          introduction: user.introduction,
          chatbotTone: user.chatbotTone,
        } as Provider;
      });

      setRowsNurse(tablePayload);
    }
  }, [dataGetProvider, isSuccessGetProvider]);

  const {
    mutateAsync: mutateAsyncArchive,
    isError: isErrorArchive,
    error: errorArchive,
    isSuccess: isSuccessArchive,
    data: dataArchive,
    isPending: isPendingArchive,
  } = useUserControllerServiceUpdateUserArchiveStatus();

  const {
    mutateAsync: mutateAsyncArchiveProvider,
    isError: isErrorArchiveNurse,
    error: errorArchiveNurse,
    isSuccess: isSuccessArchiveNurse,
    data: dataArchiveNurse,
  } = useProviderControllerServiceUpdateProviderArchiveStatus();

  const confirmDelete = async () => {
    if (selectedFilterOpt === "Nurse") {
      await mutateAsyncArchiveProvider({
        providerId: selectedProvider?.uuid || "",
        status: true,
        xTenantId: xTenantIdVal,
      });
      refetchGetProvider();
    } else {
      await mutateAsyncArchive({
        userId: selectedStaff?.uuid || "",
        status: true,
        xTenantId: xTenantIdVal,
      });
      refetch();
    }

    setOpenConfirmDeletePopUp(false);
  };

  const confirmRestore = async () => {
    if (selectedFilterOpt === "Nurse") {
      await mutateAsyncArchiveProvider({
        providerId: selectedProvider?.uuid || "",
        status: false,
        xTenantId: xTenantIdVal,
      });
      refetchGetProvider();
    } else {
      await mutateAsyncArchive({
        userId: selectedStaff?.uuid || "",
        status: false,
        xTenantId: xTenantIdVal,
      });
      refetch();
    }

    setOpenConfirmRestorePopUp(false);
  };

  useApiFeedback(
    isErrorArchiveNurse,
    errorArchiveNurse,
    isSuccessArchiveNurse,
    (dataArchiveNurse?.message || "Nurse archive status updated!") as string
  );
  useApiFeedback(
    isErrorArchive,
    errorArchive,
    isSuccessArchive,
    (dataArchive?.message || "User archive status updated!") as string
  );

  const handlePageChange = (_event: ChangeEvent<unknown> | null, page: number) => {
    setPage(page);
  };

  const handleOnClickLinkStaff = (user: User) => {
    setSelectedStaff(user);
    handleDrawer.details("View Staff", "Staff", user as User);
  };

  const handleOnClickProvider = (user: Provider) => {
    setSelectedProvider(user);
    handleDrawer.details("View Nurse", "Nurse", user as Provider);
  };

  useEffect(() => {
    dispatch(setIsLoading(isLoading || isPendingArchive || isLoading));
  }, [dispatch, isLoading, isPendingArchive, isLoading]);

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handleSorting = (column: string) => {
    if (column == "Name") {
      setSortBy("firstName");
    } else if (column == "Status") {
      setSortBy("active");
    }
    setSortDirection((prev) => (prev == "desc" ? "asc" : "desc"));
  };

  const handleDrawer = {
    staffForm: (action: string, user?: User) => {
      openDrawer({
        title: `${action} Staff`,
        component: (
          <StaffForm
            isEdit={Boolean(user)}
            staffData={user || null}
            handleDrawerClose={closeDrawer}
            xTenantId={xTenantIdVal}
            roleOptions={listType === "PG" ? RolesOfPGUsers : RolesOfAdminUsers}
          />
        ),
      });
    },
    nurseForm: (action: string, user?: Provider) => {
      openDrawer({
        title: `${action} Nurse`,
        component: <NurseForm nurse={user || null} handleDrawerClose={closeDrawer} isEdit={Boolean(user)} />,
      });
    },
    details: (title: string, role: string, user?: User | Provider) => {
      openDrawer({
        title: title,
        component: (
          <DrawerBody>
            {role === "Nurse" ? (
              <ProfileNurse providerId={user?.uuid || ""} />
            ) : (
              <ProfileStaff staffId={user?.uuid || ""} xTenantId={xTenantIdVal} />
            )}
          </DrawerBody>
        ),
      });
    },
  };

  return (
    <Grid height={"100%"}>
      <Grid
        border={`1px solid ${theme.palette.grey[300]}`}
        boxShadow={`0px 0px 16px 0px #021D2614`}
        height={"100%"}
        borderRadius={"8px"}
        container
        flexDirection={"column"}
      >
        <Grid container p={2} justifyContent={"space-between"}>
          <Grid container alignItems={"center"} columnGap={2}>
            <CustomSelectorSq
              widthOfBtn={"200px"}
              options={filterOptions}
              onSelect={(selectedOption) => {
                setSelectedFilterOpt(selectedOption);
                setPage(0);
                setSize(10);
              }}
              selectedValue={selectedFilterOpt}
            />
          </Grid>
          <Grid container columnGap={2}>
            <Grid>
              <CustomInput
                hasStartSearchIcon
                placeholder={selectedFilterOpt === "Nurse" ? "Search by Name and NPI" : "Search by Name"}
                name={"searchString"}
                value={searchStrings}
                onDebounceCall={(debouncedValue) => {
                  setPage(0);
                  setSize(10);
                  setSearchstring(debouncedValue);
                }}
                onInputEmpty={() => setSearchstring("")}
              />
            </Grid>
            <Grid>
              <Button
                startIcon={<AddIcon />}
                onClick={() => {
                  if (isPGActive) {
                    if (selectedFilterOpt !== "Nurse") {
                      handleDrawer.staffForm("Add");
                    } else {
                      handleDrawer.nurseForm("Add");
                    }
                  } else {
                    setOpenWarningPopUp(true);
                  }
                }}
                variant="contained"
                type="submit"
              >
                <Typography variant="bodySmall">{selectedFilterOpt === "Nurse" ? "Add Nurse	" : "Add Staff"}</Typography>
              </Button>
            </Grid>
          </Grid>
        </Grid>

        {/* Table */}
        <Grid>
          <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
            <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
              <TableHead>
                <TableRow>
                  {headersArr.map((header, index) => {
                    return (
                      <TableCell
                        sx={{
                          ...heading,
                          minWidth: header.minWidth ? header.minWidth : "inherit",
                          maxWidth: header.maxWidth ? header.maxWidth : "inherit",
                        }}
                        align="left"
                        key={index}
                      >
                        {header.header === "Name" ? (
                          <Link
                            style={{
                              color: "#667085",
                              textDecoration: "none",
                              cursor: "pointer",
                            }}
                            onClick={() => handleSorting(header.header)}
                          >
                            <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                              {header.header}
                              <Typography mt={0.3}>
                                {sortBy == "firstName" && sortDirection == "asc" ? (
                                  <ArrowUpwardIcon fontSize="small" />
                                ) : (
                                  <ArrowDownwardIcon fontSize="small" />
                                )}
                              </Typography>
                            </Typography>
                          </Link>
                        ) : header.header === "Status" ? (
                          <Link
                            style={{
                              color: "#667085",
                              textDecoration: "none",
                              cursor: "pointer",
                            }}
                            onClick={() => handleSorting(header.header)}
                          >
                            <Typography mt={0.6} fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                              {header.header}
                              <Typography mt={0.3}>
                                {sortBy == "active" && sortDirection == "asc" ? (
                                  <ArrowUpwardIcon fontSize="small" />
                                ) : (
                                  <ArrowDownwardIcon fontSize="small" />
                                )}
                              </Typography>
                            </Typography>
                          </Link>
                        ) : (
                          <Grid
                            container
                            flexDirection={"column"}
                            alignContent={header.header === "Actions" ? `flex-end` : "flex-start"}
                          >
                            <Typography variant="bodySmall">{header.header}</Typography>
                          </Grid>
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              </TableHead>
              <TableBody>
                {selectedFilterOpt !== "Nurse" && (
                  <>
                    {rows.length > 0 ? (
                      rows.map((user: User, index: number) => (
                        <>
                          <TableRow key={index}>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Link
                                  style={{
                                    ...linkCss,
                                    textDecoration: "none",
                                  }}
                                  onClick={() => handleOnClickLinkStaff(user)}
                                >
                                  <Typography fontWeight={550} variant="bodySmall">
                                    {user.firstName + " " + user.lastName}
                                  </Typography>
                                </Link>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user.email || "-"}
                                </Typography>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user?.address?.line1
                                    ? `${user?.address?.line1 || "-"},  ${user?.address?.line2 || "-"},
									${user?.address?.city || "-"}, ${user?.address?.state || "-"},
									${user?.address?.country || "-"}, ${user?.address?.zipcode || "-"}`
                                    : "-"}
                                </Typography>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user.phone}
                                </Typography>
                              </Grid>
                            </TableCell>

                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Status status={`${user.active ? "ACTIVE" : "INACTIVE"}`} width="74px" />
                              </Grid>
                            </TableCell>

                            <TableCell sx={{ ...heading }} align="right">
                              <Grid container justifyContent={"flex-end"} columnGap={1.2} flexWrap={"nowrap"}>
                                <IconButton
                                  sx={{ padding: "0px 5px" }}
                                  aria-label="edit"
                                  disabled={!isPGActive}
                                  onClick={() => {
                                    setSelectedStaff(user);
                                    handleDrawer.staffForm("Edit", user);
                                  }}
                                >
                                  <EditOutlinedIcon sx={iconStyles} />
                                </IconButton>
                                {!user.archive ? (
                                  <IconButton
                                    aria-label="delete"
                                    disabled={!isPGActive}
                                    onClick={() => {
                                      setSelectedStaff(user);
                                      setOpenConfirmDeletePopUp(true);
                                    }}
                                    sx={{ padding: "0px" }}
                                  >
                                    <ArchiveOutlinedIcon sx={iconStyles} />
                                  </IconButton>
                                ) : (
                                  <IconButton
                                    aria-label="delete"
                                    disabled={!isPGActive}
                                    onClick={() => {
                                      setSelectedStaff(user);
                                      setOpenConfirmRestorePopUp(true);
                                    }}
                                    sx={{ padding: "0px" }}
                                  >
                                    <RestoreIcon sx={iconStyles} />
                                  </IconButton>
                                )}
                              </Grid>
                            </TableCell>
                          </TableRow>
                        </>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={selectedFilterOpt === "Nurse" ? 9 : 6} align="center">
                          <Typography variant="bodySmall" fontWeight={550}>
                            No records found
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </>
                )}
                {selectedFilterOpt === "Nurse" && (
                  <>
                    {rowsNurse.length > 0 ? (
                      rowsNurse.map((user: Provider, index: number) => (
                        <>
                          <TableRow key={index}>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Link
                                  style={{
                                    ...linkCss,
                                    textDecoration: "none",
                                  }}
                                  onClick={() => handleOnClickProvider(user)}
                                >
                                  <Typography fontWeight={550} variant="bodySmall">
                                    {user.firstName + " " + user.lastName}
                                  </Typography>
                                </Link>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user.email || "-"}
                                </Typography>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user?.address?.line1
                                    ? `${user?.address?.line1 || "-"},  ${user?.address?.line2 || "-"},
									${user?.address?.city || "-"}, ${user?.address?.state || "-"},
									${user?.address?.country || "-"}, ${user?.address?.zipcode || "-"}`
                                    : "-"}
                                </Typography>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user.phone}
                                </Typography>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Typography sx={typographyCss} variant="bodySmall">
                                  {user.npi}
                                </Typography>
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                {user.providerLicenseDetails?.map((val) => (
                                  <>
                                    {val.licensedStates?.map((v) => (
                                      <>
                                        {v.state}
                                        <br />
                                      </>
                                    ))}
                                  </>
                                ))}
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                {user.providerLicenseDetails?.map((item) => (
                                  <>
                                    <Typography sx={typographyCss} variant="bodySmall">
                                      {item.licenseNumber}{" "}
                                    </Typography>
                                  </>
                                ))}
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                {user.providerLicenseDetails?.map((item) => (
                                  <>
                                    <Typography sx={typographyCss} variant="bodySmall">
                                      {item.expiryDate ? format(new Date(item.expiryDate || ""), "MM-dd-yyyy") : "-"}{" "}
                                    </Typography>
                                  </>
                                ))}
                              </Grid>
                            </TableCell>
                            <TableCell sx={{ ...heading }} align="left">
                              <Grid container flexDirection={"column"}>
                                <Status status={`${user.active ? "ACTIVE" : "INACTIVE"}`} width="74px" />
                              </Grid>
                            </TableCell>

                            <TableCell sx={{ ...heading }} align="right">
                              <Grid container justifyContent={"flex-end"} columnGap={1.2} flexWrap={"nowrap"}>
                                <IconButton
                                  sx={{ padding: "0px 5px" }}
                                  aria-label="edit"
                                  onClick={() => {
                                    setSelectedProvider(user);
                                    handleDrawer.nurseForm("Edit", user);
                                  }}
                                >
                                  <EditOutlinedIcon sx={iconStyles} />
                                </IconButton>
                                {!user.archive ? (
                                  <IconButton
                                    aria-label="delete"
                                    onClick={() => {
                                      setSelectedProvider(user);
                                      setOpenConfirmDeletePopUp(true);
                                    }}
                                    sx={{ padding: "0px" }}
                                  >
                                    <ArchiveOutlinedIcon sx={iconStyles} />
                                  </IconButton>
                                ) : (
                                  <IconButton
                                    aria-label="delete"
                                    onClick={() => {
                                      setSelectedProvider(user);
                                      setOpenConfirmRestorePopUp(true);
                                    }}
                                    sx={{ padding: "0px" }}
                                  >
                                    <RestoreIcon sx={iconStyles} />
                                  </IconButton>
                                )}
                              </Grid>
                            </TableCell>
                          </TableRow>
                        </>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={selectedFilterOpt === "Nurse" ? 9 : 6} align="center">
                          <Typography variant="bodySmall" fontWeight={550}>
                            No records found
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <Grid container>
            <Paginator
              page={page}
              totalPages={totalPages}
              totalRecord={totalElements}
              defaultSize={size}
              onRecordsPerPageChange={handleRecordsPerPageChange}
              onPageChange={handlePageChange}
            />
          </Grid>
        </Grid>

        <ConfirmationPopUp
          open={openConfirmDeletePopUp}
          confirmButtonName="Archive"
          onlyMsg={false}
          rowData={[
            selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
              ? `${selectedProvider?.firstName} ${selectedProvider?.lastName}` || ""
              : `${selectedStaff?.firstName} ${selectedStaff?.lastName}` || "",
            selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
              ? selectedProvider?.email || ""
              : selectedStaff?.email || "",
          ]}
          header={[{ header: "Name" }, { header: "Email" }]}
          title={`Archive Item`}
          subtitle={"Are you sure you want to archive the following item?"}
          onClose={() => setOpenConfirmDeletePopUp(false)}
          onConfirm={() => confirmDelete()}
          message={`Do you really want to archive ${
            selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
              ? `${selectedProvider?.firstName} ${selectedProvider?.lastName}`
              : `${selectedStaff?.firstName} ${selectedStaff?.lastName}` || "this user"
          } ?`}
        />
        <ConfirmationPopUp
          title={`Restore Item`}
          onlyMsg={false}
          confirmButtonName="Restore"
          subtitle={"Are you sure you want to restore the following items?"}
          open={openConfirmRestorePopUp}
          onClose={() => setOpenConfirmRestorePopUp(false)}
          onConfirm={() => confirmRestore()}
          message={`Do you really want to restore ${
            selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
              ? `${selectedProvider?.firstName} ${selectedProvider?.lastName}`
              : `${selectedStaff?.firstName} ${selectedStaff?.lastName}` || "this user"
          } ?`}
          rowData={[
            selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
              ? `${selectedProvider?.firstName} ${selectedProvider?.lastName}` || ""
              : `${selectedStaff?.firstName} ${selectedStaff?.lastName}` || "",
            selectedFilterOpt === "Nurse" || selectedFilterOpt === "Provider"
              ? selectedProvider?.email || ""
              : selectedStaff?.email || "",
          ]}
          header={[{ header: "Name" }, { header: "Email" }]}
        />
        <ConfirmationPopUp
          onlyMsg={true}
          open={openWarningPopUp}
          onClose={() => setOpenWarningPopUp(false)}
          message={`This provider group is inactive, you can not add staff for it.`}
          title={""}
          rowData={[]}
          header={[]}
        />
      </Grid>
    </Grid>
  );
};

export default StaffList;
