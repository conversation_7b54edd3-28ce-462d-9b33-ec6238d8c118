import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";

import AddIcon from "@mui/icons-material/Add";
import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import RestoreIcon from "@mui/icons-material/Restore";
import {
  Button,
  IconButton,
  Link,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";

import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { format } from "date-fns";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomInput from "@/common-components/custom-input/custom-input";
import Paginator from "@/common-components/paginator/paginator";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";
import { heading, iconStyles, tableCellCss, typographyCss } from "@/common-components/table/common-table-widgets";
import Toggle from "@/common-components/toggle/toggle";

import useApiFeedback from "@/hooks/useApiFeedback";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { ContentObject } from "@/models/response/response-content-entity";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useCarePlanControllerServiceUpdateCarePlanArchiveStatus1 } from "@/sdk/queries";
import { CarePlan, CarePlanControllerService } from "@/sdk/requests";
import { toCamelCase } from "@/utils/toCamelCase";

const headerName = ["Title", "Track", "Duration", "Modified on", "Status", "Action"];

// Add popup table headers
const popupHeaders = [{ header: "Title" }, { header: "Track" }, { header: "Duration" }];

function CarePlanTab() {
  const dispatch = useDispatch();
  const [searchAssignCarePlan, setSearchAssignCarePlan] = useState("");
  const navigate = useNavigate();
  const [carePlanDetails, setCarePlanDetails] = useState<CarePlan[]>();
  const xTenantId = "eAmata";

  const [selectedCarePlan, setSelectedCarePlan] = useState<CarePlan>();
  const [openConfirmDeletePopUp, setOpenConfirmDeletePopUp] = useState(false);
  const [openConfirmRestorePopUp, setOpenConfirmRestorePopUp] = useState(false);

  // Pagination state
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState<number>(0);
  const [size, setSize] = useState(10);

  // Sorting state
  const [sortBy, setSortBy] = useState("title");
  const [sortDirection, setSortDirection] = useState("desc");

  const {
    data: CarePlanGetData,
    isSuccess,
    refetch,
    isLoading,
  } = useQuery({
    queryKey: ["get-care-plan", searchAssignCarePlan, page, size, sortBy, sortDirection],
    queryFn: () =>
      CarePlanControllerService.getAllCarePlans1({
        searchString: searchAssignCarePlan,
        page,
        size,
        sortBy,
        sortDirection,
      }),
  });

  useEffect(() => {
    if (isSuccess) {
      const response = (CarePlanGetData as unknown as AxiosResponse).data as ContentObject<CarePlan[]>;
      const userData = response?.content;
      setTotalPages(response?.page?.totalPages as number);
      setTotalElements(response?.page?.totalElements as number);
      setCarePlanDetails(userData);
    }
  }, [isSuccess, CarePlanGetData]);

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handlePageChange = (_event: ChangeEvent<unknown> | null, page: number) => {
    setPage(page);
  };

  const handleSorting = (column: string) => {
    if (column === "Title") {
      setSortBy("title");
    } else if (column === "Status") {
      setSortBy("active");
    } else if (column === "Duration") {
      setSortBy("duration");
    }
    setSortDirection((prev) => (prev === "desc" ? "asc" : "desc"));
  };

  const {
    mutateAsync: mutateAsyncArchive,
    isError: isErrorArchive,
    error: errorArchive,
    // isPending: isPendingArchive,
    isSuccess: isSuccessArchive,
    data: dataArchive,
  } = useCarePlanControllerServiceUpdateCarePlanArchiveStatus1();

  const confirmDelete = async () => {
    await mutateAsyncArchive({
      carePlanId: selectedCarePlan?.uuid || "",
      status: true,
      xTenantId: xTenantId,
    });
    await refetch();
    setOpenConfirmDeletePopUp(false);
  };

  const confirmRestore = async () => {
    await mutateAsyncArchive({
      carePlanId: selectedCarePlan?.uuid || "",
      status: false,
      xTenantId: xTenantId,
    });
    await refetch();
    setOpenConfirmRestorePopUp(false);
  };

  useApiFeedback(
    isErrorArchive,
    errorArchive,
    isSuccessArchive,
    (dataArchive?.message || "Care Plan archive status updated!") as string
  );

  const handleEditDate = (data: CarePlan) => {
    navigate(`/admin/settings/careplan`, { state: { EditData: data } });
  };

  const renderTableCell = (data: string | number) => {
    return (
      <TableCell sx={{ ...heading }} align="left">
        <Grid container flexDirection={"column"}>
          <Typography sx={typographyCss} variant="bodySmall">
            {data}
          </Typography>
        </Grid>
      </TableCell>
    );
  };

  const updateCarePlanStatus = useMutation({
    mutationFn: ({ carePlanId, status, xTenantId }: { carePlanId: string; status: boolean; xTenantId: string }) =>
      CarePlanControllerService.updateUserStatus({
        carePlanId,
        status,
        xTenantId,
      }),
    onSuccess: () => {
      refetch();
      dispatch(setSnackbarOn({ severity: AlertSeverity.SUCCESS, message: "Care Plan status updated successfully!" }));
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  // Create row data for popup when archiving
  const getPopupRowData = (plan: CarePlan) => {
    return [
      plan.title || "-",
      plan.trackedVitals ? toCamelCase(plan.trackedVitals[0]) : "-",
      `${plan.duration || ""} ${toCamelCase(plan.durationUnit || "")}`,
    ];
  };

  return (
    <Grid border={"1px solid #EAECF0"} borderRadius={2}>
      <Grid container justifyContent={"space-between"} mb={1} px={2} pt={2}>
        <Grid container alignItems={"center"} gap={2} display={"flex"} flexDirection={"row"}>
          <Grid>
            <Typography fontSize={"18px"} fontWeight={500}>
              Care Plans
            </Typography>
          </Grid>
        </Grid>
        <Grid container alignItems={"center"} gap={2} display={"flex"} flexDirection={"row"}>
          <Grid>
            <CustomInput
              placeholder="Search Care Plan"
              name="careplan"
              hasStartSearchIcon={true}
              value={searchAssignCarePlan}
              onDebounceCall={(searchString) => setSearchAssignCarePlan(searchString)}
              onInputEmpty={() => setSearchAssignCarePlan("")}
            />
          </Grid>
          <Grid>
            <Button
              startIcon={<AddIcon />}
              variant="contained"
              sx={{ borderRadius: "8px" }}
              onClick={() => {
                navigate(`/admin/settings/careplan`);
              }}
            >
              Add Care Plan
            </Button>
          </Grid>
        </Grid>
      </Grid>
      <Grid>
        <TableContainer sx={{ maxHeight: "60vh", overflow: "auto" }}>
          <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
            <TableHead>
              <TableRow>
                {headerName.map((header, index) => (
                  <TableCell
                    sx={{
                      ...heading,
                    }}
                    align="left"
                    key={index}
                  >
                    {header === "Title" || header === "Status" || header === "Duration" ? (
                      <Link
                        style={{
                          color: "#667085",
                          textDecoration: "none",
                          cursor: "pointer",
                        }}
                        onClick={() => handleSorting(header)}
                      >
                        <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                          {header}
                          <Typography mt={0.3}>
                            {sortBy === header.toLowerCase() && sortDirection === "asc" ? (
                              <ArrowUpwardIcon fontSize="small" />
                            ) : (
                              <ArrowDownwardIcon fontSize="small" />
                            )}
                          </Typography>
                        </Typography>
                      </Link>
                    ) : (
                      header
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                Array.from({ length: headerName.length }).map((_, index) => (
                  <TableRow key={index}>
                    {headerName.map((_, cellIndex) => (
                      <TableCell key={cellIndex}>
                        <Skeleton variant="text" width={200} height={30} sx={{ borderRadius: "10px" }} />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : carePlanDetails && carePlanDetails?.length > 0 ? (
                carePlanDetails?.map((plan) => (
                  <TableRow key={plan.uuid}>
                    {renderTableCell(plan.title ?? "-")}
                    {renderTableCell(plan?.trackedVitals ? toCamelCase(plan?.trackedVitals[0]) : "-")}
                    {renderTableCell(`${plan.duration ?? ""} ${toCamelCase(plan.durationUnit ?? "")}`)}
                    {renderTableCell(format(plan?.modified ?? "-", "dd MMM yyyy HH:mm a") ?? "-")}
                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexDirection={"column"}>
                        <Toggle
                          status={plan.active ?? false}
                          handleStatusChange={() => {
                            updateCarePlanStatus.mutate({
                              carePlanId: plan.uuid || "",
                              status: !plan.active,
                              xTenantId: xTenantId,
                            });
                          }}
                        />
                      </Grid>
                    </TableCell>

                    <TableCell sx={{ ...heading }} align="left">
                      <Grid container flexWrap={"nowrap"}>
                        <IconButton sx={{ padding: "0px 5px" }} aria-label="edit" onClick={() => handleEditDate(plan)}>
                          <EditOutlinedIcon sx={iconStyles} />
                        </IconButton>
                        {!plan.archive ? (
                          <IconButton
                            aria-label="delete"
                            sx={{ padding: "0px" }}
                            onClick={() => {
                              setSelectedCarePlan(plan), setOpenConfirmDeletePopUp(true);
                            }}
                          >
                            <ArchiveOutlinedIcon sx={iconStyles} />
                          </IconButton>
                        ) : (
                          <IconButton
                            aria-label="unarchive"
                            sx={{ padding: "0px" }}
                            onClick={() => {
                              setSelectedCarePlan(plan), setOpenConfirmRestorePopUp(true);
                            }}
                          >
                            <RestoreIcon sx={iconStyles} />
                          </IconButton>
                        )}
                      </Grid>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={headerName.length} align="center">
                    <Typography variant="bodySmall" fontWeight={550}>
                      No records found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <Paginator
          page={page}
          totalPages={totalPages}
          totalRecord={totalElements}
          defaultSize={size}
          onRecordsPerPageChange={handleRecordsPerPageChange}
          onPageChange={handlePageChange}
        />
        <ConfirmationPopUp
          open={openConfirmDeletePopUp}
          confirmButtonName="Archive"
          onClose={() => setOpenConfirmDeletePopUp(false)}
          onConfirm={() => confirmDelete()}
          message={`Do you really want to archive this Care Plan?`}
          title={`Archive Item`}
          subtitle={"Are you sure you want to archive the following Care plan?"}
          onlyMsg={false}
          header={popupHeaders}
          rowData={selectedCarePlan ? getPopupRowData(selectedCarePlan) : []}
        />

        <ConfirmationPopUp
          open={openConfirmRestorePopUp}
          onClose={() => setOpenConfirmRestorePopUp(false)}
          onConfirm={() => confirmRestore()}
          message={`Do you really want to restore this Care Plan?`}
          title={`Restore Item`}
          subtitle={"Are you sure you want to restore the following Care plan?"}
          confirmButtonName="Restore"
          onlyMsg={false}
          header={popupHeaders}
          rowData={selectedCarePlan ? getPopupRowData(selectedCarePlan) : []}
        />
      </Grid>
    </Grid>
  );
}

export default CarePlanTab;
