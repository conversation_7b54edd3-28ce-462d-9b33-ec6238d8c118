import { <PERSON><PERSON><PERSON><PERSON>, FormProvider, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import { Button, Grid2 as Grid, Stack, Typography } from "@mui/material";
import { Box } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import Autocomplete from "@/components/ui/Form/Autocomplete";
import { Input } from "@/components/ui/Form/Input";
import { InputPhoneNumber } from "@/components/ui/Form/InputPhoneNumber";
import { UploadImage } from "@/components/ui/Form/UploadImage";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { User, UserControllerService } from "@/sdk/requests";
import { splitPhoneNumber } from "@/services/common/phone-formatter";
import { stateList } from "@/utils/StateList";
import { theme } from "@/utils/theme";

import { editProfileSchema } from "./profile-schema";

interface EditProfileProps {
  handleOnSuccess: () => void;
}

const EditProfile = ({ handleOnSuccess }: EditProfileProps) => {
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const { data: profile, isLoading: loadingProfile } = useQuery({
    queryKey: ["my-profile"],
    queryFn: async () => {
      const response = (await UserControllerService.getProfile1()) as AxiosResponse<User>;

      return response.data as User;
    },
  });

  const initialValues = {
    firstName: profile?.firstName || "",
    lastName: profile?.lastName || "",
    email: profile?.email || "",
    phone: profile?.phone ? splitPhoneNumber(profile?.phone)?.number : "" || "",
    prefix: profile?.phone ? splitPhoneNumber(profile?.phone)?.countryCode : "+1",
    address: {
      line1: profile?.address?.line1 || "",
      line2: profile?.address?.line2 || "",
      city: profile?.address?.city || "",
      state: profile?.address?.state || "",
      zipcode: profile?.address?.zipcode || "",
      country: "USA",
    },
    avatar: "",
  };

  const formMethods = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(editProfileSchema),
  });

  const editProfileService = useMutation({
    mutationFn: ({ user, avatar }: { user: User; avatar?: string }) =>
      Promise.all([
        UserControllerService.updateUser({
          requestBody: { ...profile, ...user, uuid: profile?.uuid },
        }),
        avatar !== "" &&
          UserControllerService.changeAvatar3({
            requestBody: {
              newAvatar: avatar?.startsWith("data:image")
                ? avatar.replace(/data:image\/(jpeg|png);base64,/, "")
                : avatar,
            },
            userUuid: profile?.uuid || "",
          }),
      ]),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["my-profile"] });
      dispatch(setSnackbarOn({ severity: AlertSeverity.SUCCESS, message: "Profile updated successfully" }));
      handleOnSuccess();
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(setSnackbarOn({ severity: AlertSeverity.ERROR, message: error.body.message }));
    },
  });

  const onSubmit = (values: FieldValues) => {
    const formValues = values as typeof initialValues;
    const payload = {
      firstName: formValues.firstName,
      lastName: formValues.lastName,
      email: formValues.email,
      address: {
        line1: formValues.address?.line1,
        line2: formValues.address?.line2,
        city: formValues.address?.city,
        state: formValues.address?.state,
        country: formValues.address?.country,
        zipcode: formValues.address?.zipcode,
      },
      phone: `${formValues.prefix}${formValues.phone}`,
    } as User;

    editProfileService.mutate({ user: payload, avatar: formValues.avatar });
  };

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onSubmit)}>
        <Stack spacing={3}>
          <Grid container spacing={2}>
            <Grid size={2}>
              <UploadImage name="avatar" defaultImage={profile?.avatar as string} isLoading={loadingProfile} />
            </Grid>
            <Grid container size={10}>
              <Grid size={6}>
                <Input name="firstName" isRequired />
              </Grid>
              <Grid size={6}>
                <Input name="lastName" isRequired />
              </Grid>
              <Grid size={6}>
                <Input name="email" isRequired disabled />
              </Grid>
              <Grid size={6}>
                <InputPhoneNumber isRequired />
              </Grid>
            </Grid>
          </Grid>
          <Box sx={{ border: `1px solid ${theme.palette.divider}`, borderRadius: 2 }}>
            <Box sx={{ padding: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
              <Typography variant="medium">Address</Typography>
            </Box>
            <Grid container spacing={2} sx={{ padding: 2 }}>
              <Grid size={6}>
                <Input name="address.line1" label="Line 1" isRequired />
              </Grid>
              <Grid size={6}>
                <Input name="address.line2" label="Line 2" />
              </Grid>
              <Grid size={6}>
                <Autocomplete
                  name="address.state"
                  label="State"
                  options={stateList.map((item) => ({ label: item.key, value: item.value }))}
                  placeholder="Select State"
                  isRequired
                />
              </Grid>
              <Grid size={6}>
                <Input name="address.city" label="City" isRequired />
              </Grid>
              <Grid size={6}>
                <Input name="address.country" label="Country" isRequired disabled />
              </Grid>
              <Grid size={6}>
                <Input name="address.zipcode" label="Zip Code" isRequired />
              </Grid>
            </Grid>
          </Box>
        </Stack>
        <Button variant="contained" type="submit" loading={editProfileService.isPending} sx={{ mt: 2 }}>
          Save Profile
        </Button>
      </form>
    </FormProvider>
  );
};

export default EditProfile;
