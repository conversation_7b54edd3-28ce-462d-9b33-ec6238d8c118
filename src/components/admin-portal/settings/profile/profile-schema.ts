import * as yup from "yup";

import {
  addressLine1Max128ErrorMsg,
  addressLine1RequiredErrorMsg,
  cityMax64ErrorMsg,
  cityRequiredErrorMsg,
  cityStateRegexErrorMsg,
  confirmNewPaswordErrorMsg,
  countryRequiredErrorMsg,
  emailRegexErrorMsg,
  emailRequiredErrorMsg,
  firstNameOrSurnameRegexErrorMsg,
  firstNameRequiredErrorMsg,
  lastNameRequiredErrorMsg,
  lessThan255ErrorMsg,
  newPasswordRequiredErrorMsg,
  passwordMustMatchErrorMsg,
  passwordRegexErrorMsg,
  stateRequiredErrorMsg,
  zipCodeRegexErrorMsg,
  zipCodeRequiredErrorMsg,
} from "../../../../constants/error-messages";
import { cityStateRgex, emailRegExp, nameRegex, passwordRegx, zipCodeRegex } from "../../../../utils/regex";

export const changePasswordSchema = yup.object().shape({
  currentPassword: yup.string().required("Current password is required"),
  newPassword: yup.string().required(newPasswordRequiredErrorMsg).matches(passwordRegx, passwordRegexErrorMsg),
  confirmNewPassword: yup
    .string()
    .required(confirmNewPaswordErrorMsg)
    .oneOf([yup.ref("newPassword") as unknown as string], passwordMustMatchErrorMsg),
});

export const editProfileSchema = yup.object().shape({
  firstName: yup.string().required(firstNameRequiredErrorMsg).matches(nameRegex, firstNameOrSurnameRegexErrorMsg),
  lastName: yup.string().required(lastNameRequiredErrorMsg).matches(nameRegex, firstNameOrSurnameRegexErrorMsg),
  phone: yup
    .string()
    .nullable()
    .transform((value) => (value === "" ? null : value)),
  prefix: yup
    .string()
    .nullable()
    .transform((value) => (value === "" ? null : value)),
  email: yup.string().required(emailRequiredErrorMsg).matches(emailRegExp, emailRegexErrorMsg),
  address: yup.object().shape({
    line1: yup.string().max(128, addressLine1Max128ErrorMsg).required(addressLine1RequiredErrorMsg),
    line2: yup.string().max(128, addressLine1Max128ErrorMsg),
    city: yup
      .string()
      .matches(cityStateRgex, cityStateRegexErrorMsg)
      .max(64, cityMax64ErrorMsg)
      .required(cityRequiredErrorMsg),
    state: yup
      .string()
      .matches(cityStateRgex, cityStateRegexErrorMsg)
      .max(50, lessThan255ErrorMsg)
      .required(stateRequiredErrorMsg),
    zipcode: yup.string().required(zipCodeRequiredErrorMsg).matches(zipCodeRegex, zipCodeRegexErrorMsg),
    country: yup.string().required(countryRequiredErrorMsg),
  }),
});
