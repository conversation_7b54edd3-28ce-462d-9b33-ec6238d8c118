import React, { useState } from "react";
import { useSearchParams } from "react-router-dom";

import { Tab, Tabs } from "@mui/material";
import { Grid } from "@mui/system";

import { CustomTabPanel, a11yProps } from "../../../common-components/custom-tab/custom-tab";
import ProviderList from "../provider/provider-list";
import RolesList from "../roles/roles-list";
import DrugLibraryTab from "../staff/drug-library/drug-library-tab";
import StaffList from "../staff/staff-list";
import CarePlanTab from "./care-plan/care-plan-tab";

const tabLabels = ["Roles", "Admin Users", "Care Plans", "Library"];

const SettingsTabs = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [value, setValue] = useState<number | null>(searchParams.get("tab") ? +searchParams.get("tab")! : 0);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSearchParams({ tab: newValue.toString() });
    setValue(newValue);
  };

  return (
    <Grid width={"100%"} height={"100%"} p={2}>
      <Grid height={"100%"} borderRadius={"8px"} container flexDirection={"column"}>
        <Grid>
          <Grid sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs value={value} onChange={handleChange}>
              {tabLabels?.map((item, index) => (
                <Tab sx={{ textTransform: "none", fontWeight: 550 }} key={index} label={item} {...a11yProps(0)} />
              ))}
            </Tabs>
          </Grid>
          <Grid flex={1}>
            {tabLabels.map((item, index) => (
              <CustomTabPanel key={index} value={value} index={index}>
                {item === "Roles" && <RolesList />}
                {item === "Providers" && <ProviderList />}
                {item === "Admin Users" && (
                  <StaffList
                    subdomain=""
                    filterOptions={["Super Admin", "Front Desk", "Biller", "Nurse"]}
                    isPGActive={true}
                    roleType={"STAFF"}
                    listType={"ADMIN"}
                  />
                )}
                {item === "Care Plans" && <CarePlanTab />}
                {item === "Library" && <DrugLibraryTab />}
              </CustomTabPanel>
            ))}
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default SettingsTabs;
