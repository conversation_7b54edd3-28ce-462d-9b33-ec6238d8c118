import { PropsWithChildren, useRef } from "react";

import CloseOutlinedIcon from "@mui/icons-material/CloseOutlined";
import { Box, Drawer, Grid2 as Grid, IconButton, Typography, useMediaQuery } from "@mui/material";

import { theme } from "@/utils/theme";

import { useDrawer } from "../providers/DrawerProvider";

interface MainDrawerProps {
  drawerWidth?: string;
  anchor?: "left" | "top" | "right" | "bottom";
}

const MainDrawer = ({ drawerWidth = "800px", anchor = "right" }: PropsWithChildren<MainDrawerProps>) => {
  const { isOpen, content: contentDrawer, close } = useDrawer();
  const belowLg = useMediaQuery(theme.breakpoints.down("lg"));
  const headerRef = useRef<HTMLDivElement>(null);

  return (
    <Drawer
      anchor={anchor}
      open={isOpen}
      // onClose={close}
      PaperProps={{
        sx: {
          width: belowLg ? "100%" : drawerWidth,
        },
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          height: "100vh",
          overflow: "hidden",
        }}
      >
        <Grid
          container
          ref={headerRef}
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            borderBottom: `1px solid ${theme.palette.divider}`,
            padding: 2,
            paddingLeft: 3,
          }}
        >
          <Typography
            sx={{
              fontWeight: 600,
              fontSize: 24,
            }}
          >
            {contentDrawer?.title}
          </Typography>
          <IconButton onClick={close} size="small">
            <CloseOutlinedIcon />
          </IconButton>
        </Grid>
        <Grid
          container
          sx={{
            height: `calc(100vh - ${headerRef.current?.offsetHeight ?? 0}px)`,
            overflow: "hidden",
          }}
        >
          {contentDrawer?.component}
        </Grid>
      </Box>
    </Drawer>
  );
};

export default MainDrawer;
