/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from "react";
import { Controller, useFormContext } from "react-hook-form";

import { CheckCircleOutline, Visibility, VisibilityOff } from "@mui/icons-material";
import { IconButton, InputAdornment, InputBase, InputBaseProps, InputLabel, Tooltip } from "@mui/material";

import { startCase } from "lodash";

interface InputProps extends Omit<InputBaseProps, "name"> {
  name: string;
  rules?: Record<string, any>;
  label?: string;
  isRequired?: boolean;
  placeholder?: string;
  isVerified?: boolean;
}

export const Input = ({
  name,
  rules,
  label,
  type = "text",
  isRequired,
  placeholder,
  isVerified,
  ...restProps
}: InputProps) => {
  const { control } = useFormContext();
  const [showPassword, setShowPassword] = useState(false);

  const { inputProps, ...rest } = restProps;

  const isHaveAdornment = type === "password" || (type === "email" && isVerified);

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      defaultValue=""
      render={({ field, fieldState: { error } }) => (
        <>
          <InputLabel
            sx={{
              mb: 1,
              fontSize: "14px",
              fontWeight: "500",
              color: "#515C5F",
            }}
          >
            {label || startCase(name)}&nbsp;
            {isRequired && <span style={{ color: "#D32F2F" }}>*</span>}
          </InputLabel>

          <InputBase
            {...field}
            error={!!error}
            fullWidth
            sx={{
              height: "43px",
              border: "1px solid #E8EBEC",
              borderRadius: "8px",
              pr: isHaveAdornment ? 1 : 0,
            }}
            inputProps={{
              type: showPassword ? "text" : type || "password",
              placeholder: placeholder || `Enter ${label || startCase(name)}`,
              style: {
                padding: isHaveAdornment ? "12px 0 12px 12px" : "12px",
                fontSize: "14px",
                background: "#FFFFFF",
                borderRadius: "8px",
                height: "100%",
                boxSizing: "border-box",
              },
              sx: {
                "&::placeholder": {
                  color: "#B6C1C4",
                  opacity: 1,
                },
              },
              ...inputProps,
            }}
            endAdornment={
              isHaveAdornment && (
                <InputAdornment position="end">
                  {type === "email" && isVerified && (
                    <Tooltip title="Email already verified">
                      <CheckCircleOutline color="success" fontSize="small" />
                    </Tooltip>
                  )}
                  {type === "password" ? (
                    <IconButton onClick={() => setShowPassword(!showPassword)} size="small">
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  ) : null}
                </InputAdornment>
              )
            }
            {...rest}
          />

          {error && (
            <InputLabel error sx={{ mt: 0.5, fontSize: "12px", whiteSpace: "normal" }}>
              {error.message}
            </InputLabel>
          )}
        </>
      )}
    />
  );
};
