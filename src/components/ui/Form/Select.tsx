/* eslint-disable  @typescript-eslint/no-explicit-any */
import * as React from "react";
import { Controller, FieldError, FieldValues, Path, useFormContext } from "react-hook-form";

import { useOption } from "@mui/base/useOption";
import { SelectProvider, useSelect } from "@mui/base/useSelect";
import UnfoldMoreRoundedIcon from "@mui/icons-material/UnfoldMoreRounded";
import { InputLabel } from "@mui/material";
import { Grid, styled } from "@mui/system";

import clsx from "clsx";
import { startCase } from "lodash";

interface SelectOption {
  label: string;
  value: string;
  disabled?: boolean;
}

interface SelectProps<T extends FieldValues> extends CustomSelectProps {
  name: Path<T>;
  rules?: object;
  defaultValue?: string;
}

export default function Select<T extends FieldValues>({
  name,
  options,
  placeholder,
  rules,
  label,
  noLabel,
  isRequired,
  disabled,
  width,
  defaultValue,
  startIcon,
}: SelectProps<T>) {
  const { control } = useFormContext();

  return (
    <Grid width={width}>
      <Controller
        name={name}
        control={control}
        rules={rules}
        defaultValue={(defaultValue as any) || null}
        render={({ field: { onChange, value }, fieldState: { error } }) => (
          <CustomSelect
            options={options}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            error={error}
            label={label || startCase(name)}
            isRequired={isRequired}
            noLabel={noLabel}
            disabled={disabled}
            startIcon={startIcon}
          />
        )}
      />
    </Grid>
  );
}

interface CustomSelectProps {
  options: SelectOption[];
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: FieldError | undefined;
  noLabel?: boolean;
  isRequired?: boolean;
  disabled?: boolean;
  width?: string;
  startIcon?: React.ReactNode;
}

export const CustomSelect = ({
  label,
  options,
  placeholder,
  value: controlledValue,
  onChange,
  error,
  noLabel = false,
  isRequired = false,
  disabled = false,
  startIcon,
}: CustomSelectProps) => {
  const listboxRef = React.useRef(null);
  const [listboxVisible, setListboxVisible] = React.useState(false);

  const { getButtonProps, getListboxProps, contextValue, value } = useSelect({
    listboxRef,
    onOpenChange: setListboxVisible,
    open: listboxVisible,
    value: controlledValue,
    onChange: (_event, newValue) => {
      onChange?.(newValue as string);
    },
  });

  return (
    <Root>
      {!noLabel && (
        <InputLabel
          sx={{
            mb: 1,
            fontSize: "14px",
            fontWeight: "500",
            color: "#515C5F",
          }}
        >
          {label}&nbsp;
          {isRequired && <span style={{ color: "#D32F2F" }}>*</span>}
        </InputLabel>
      )}

      <Toggle {...getButtonProps()} className={clsx({ error })} disabled={disabled}>
        {startIcon && <span style={{ marginRight: "8px" }}>{startIcon}</span>}
        {renderSelectedValue(value as string, options) || (
          <span style={{ color: "#B6C1C4" }}>{placeholder ?? "Please Select"}</span>
        )}
        <UnfoldMoreRoundedIcon />
      </Toggle>

      <Listbox {...getListboxProps()} className={listboxVisible ? "" : "hidden"}>
        <SelectProvider value={contextValue}>
          {options.map((option) => (
            <CustomOption key={option.value} value={option.value} disabled={option.disabled}>
              {option.label}
            </CustomOption>
          ))}
        </SelectProvider>
      </Listbox>

      {error && (
        <InputLabel error sx={{ mt: 0.5, fontSize: "12px", whiteSpace: "normal" }}>
          {error.message}
        </InputLabel>
      )}
    </Root>
  );
};

function renderSelectedValue(value: string, options: SelectOption[]) {
  const selectedOption = options.find((option) => option.value === value);
  return selectedOption?.label || null;
}

interface CustomOptionProps {
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  value: string;
}

function CustomOption(props: CustomOptionProps) {
  const { children, value, className, disabled = false } = props;
  const { getRootProps, highlighted } = useOption({
    value,
    disabled,
    label: children,
  });

  return (
    <Option {...getRootProps()} className={clsx({ highlighted }, className)}>
      {children}
    </Option>
  );
}

const Toggle = styled("button")(
  () => `
  font-size: 14px;
  box-sizing: border-box;
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  text-align: left;
  background: #FFF;
  border: 1px solid #E8EBEC;
  color: #212D30;
  position: relative;
  height: 43px;
  &:hover {
    background: #F3F6F9;
    border-color: #C7D0DD;
  }
  &:focus-visible {
    outline: 0;
    border-color: #3399FF;
    box-shadow: 0 0 0 3px #99CCF3;
  }
  &:disabled {
    background: #F3F6F9;
    border-color: #E8EBEC;
    color: #B6C1C4;
  }
  & > svg {
    font-size: 1rem;
    position: absolute;
    height: 100%;
    top: 0;
    right: 10px;
  }
  `
);

const Listbox = styled("ul")(
  () => `
  font-size: 14px;
  box-sizing: border-box;
  min-height: calc(1.5em + 22px);
  max-height: 300px;
  border-radius: 8px;
  text-align: left;
  line-height: 1.5;
  background: #FFF;
  border: 1px solid #E8EBEC;
  color: #212D30;
  padding: 5px;
  margin: 5px 0 0;
  position: absolute;
  height: auto;
  width: 100%;
  overflow: auto;
  z-index: 999;
  outline: 0;
  list-style: none;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  &.hidden {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.4s ease, visibility 0.4s step-end;
  }
  `
);

const Option = styled("li")(
  () => `
  padding: 8px;
  border-radius: 8px;
  &.highlighted,
  &:hover {
    background-color: #E5EAF2;
    color: #1C2025;
  }
  &:focus-visible {
    outline: 3px solid #99CCF3;
  }
  &::before {
    content: '';
    width: 1ex;
    height: 1ex;
    margin-right: 1ex;
    display: inline-block;
    border-radius: 50%;
    vertical-align: middle;
  }
  `
);

const Root = styled("div")`
  position: relative;
`;
