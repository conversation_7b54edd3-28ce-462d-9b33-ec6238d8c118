import { Grid, Menu, MenuItem } from "@mui/material";
import React, { ChangeEvent, useEffect, useRef, useState } from "react";
import { toCamelCase } from "../../utils/toCamelCase";
import CustomInput from "../custom-input/custom-input";

type CustomSuggestionInputProp = {
	options: { key: string; value: string }[];
	placeholder: string;
	name: string;
	value: string;
	onChange: (e: string) => void;
	hasError?: boolean;
	errorMessage?: string;
};

const CustomSuggestionInput = (props: CustomSuggestionInputProp) => {
	const { options, placeholder, name, value, hasError, errorMessage } = props;
	const [valueString, setValueString] = useState(value || "");
	const [searchString, setSearchString] = useState("");
	const [listOfOptions, setListOfOptions] = useState(options);

	const optionsCloned = structuredClone(options);
	useEffect(() => {
		// setValueString("");
		setListOfOptions(optionsCloned);
	}, [options]);

	const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
	const open = Boolean(anchorEl);
	const buttonRef = useRef(null);

	const handleChangeInput = (event: ChangeEvent<HTMLInputElement>) => {
		setAnchorEl(null);
		setSearchString(event.target.value);
		props.onChange(event.target.value);
	};

	const handleCloseLocationMenu = () => {
		setAnchorEl(null);
	};

	const handleMenuItemClick = (
		event: React.MouseEvent<HTMLElement>,
		item: { key?: string; value: string }
	) => {
		if (!event) {
			return;
		}
		setValueString(item.value);
		setAnchorEl(null);
		props.onChange(item.value);
	};

	const handleOnClick = () => {
		if (listOfOptions.length > 0) {
			setAnchorEl(buttonRef.current);
		}
	};

	useEffect(() => {
		if (searchString) {
			setAnchorEl(null);
			setValueString(searchString);
			const searchedOption =
				listOfOptions.filter((option) => option.value.includes(searchString)) ||
				[];
			if (searchedOption.length > 0) {
				setListOfOptions(searchedOption);
				setAnchorEl(buttonRef.current);
			}
		} else {
			setListOfOptions(optionsCloned);
		}
	}, [searchString]);

	return (
		<div>
			<Grid
				ref={buttonRef}
				id="basic-button"
				aria-controls={open ? "basic-menu" : undefined}
				aria-haspopup="true"
				aria-expanded={open ? "true" : undefined}
			>
				<CustomInput
					placeholder={placeholder}
					name={name}
					value={valueString}
					onChange={handleChangeInput}
					onClickNotify={handleOnClick}
					hasOpenListArrow
					hasError={hasError || false}
					errorMessage={errorMessage || ""}
				/>
			</Grid>
			<Menu
				id="basic-menu"
				anchorEl={anchorEl}
				slotProps={{
					paper: {
						sx: {
							maxHeight: "400px",
							width: "25ch",
							height: "200px",
						},
					},
				}}
				open={open}
				anchorOrigin={{
					vertical: "bottom",
					horizontal: "left",
				}}
				transformOrigin={{
					vertical: "top",
					horizontal: "left",
				}}
				MenuListProps={{
					"aria-labelledby": "basic-button",
				}}
				onClose={handleCloseLocationMenu}
			>
				{listOfOptions.length > 0 &&
					listOfOptions.map((item, index) => (
						<MenuItem
							sx={{ width: "200px" }}
							key={index}
							onClick={(event) => handleMenuItemClick(event, item)}
						>
							{toCamelCase(item.value)}
						</MenuItem>
					))}
			</Menu>
		</div>
	);
};

export default CustomSuggestionInput;
