import { Grid, List, ListItem, ListItemText } from "@mui/material";
import Popper from "@mui/material/Popper";
import * as React from "react";
import { theme } from "../../utils/theme";
import CustomInput from "../custom-input/custom-input";

type CustomSuggestionInputProp = {
	options: { key: string; value: string }[];
	placeholder: string;
	name: string;
	value: string;
	onChange: (e: string) => void;
	hasError?: boolean;
	errorMessage?: string;
	width?: string;
	isDisabled?: boolean;
};

export default function CustomSuggestionInput2(
	props: CustomSuggestionInputProp
) {
	const {
		options,
		placeholder,
		name,
		width,
		value,
		hasError,
		errorMessage,
		isDisabled,
	} = props;
	const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
	const [valueString, setValueString] = React.useState(value || "");
	const [searchString, setSearchString] = React.useState("");
	const buttonRef = React.useRef(null);

	const [listOfOptions, setListOfOptions] = React.useState(options);
	const optionsCloned = structuredClone(options);
	React.useEffect(() => {
		setListOfOptions(optionsCloned);
	}, [options]);

	React.useEffect(() => {
		setValueString(value);
	}, [value]);

	const handleClick = (event: React.ChangeEvent<HTMLInputElement>) => {
		setSearchString(event.target.value);
		setListOfOptions([]);
		props.onChange(event.target.value);
		if (anchorEl && event.target.value) return;

		setAnchorEl(anchorEl ? null : event.currentTarget);

		const el = document.querySelector("#el");
		window.addEventListener("click", (e) => {
			if (e.target === el) return;
			setAnchorEl(null);
		});
	};

	// const handleOnClick = () => {
	//   setAnchorEl(anchorEl ? null : buttonRef.current);
	// };

	const handleMenuItemClick = (
		event: React.MouseEvent<HTMLElement>,
		item: { key?: string; value: string }
	) => {
		if (!event) {
			return;
		}
		setValueString(item.value);
		setAnchorEl(null);
		props.onChange(item.value);
		setListOfOptions(optionsCloned);
	};

	const boxClick = () => {
		window.addEventListener("click", () => {});
	};

	const open = Boolean(anchorEl);
	const id = open ? "simple-popper" : undefined;

	React.useEffect(() => {
		if (searchString) {
			setAnchorEl(null);
			setValueString(searchString);
			const searchedOption =
				optionsCloned.filter((option) =>
					option.value.toLocaleLowerCase().includes(searchString.toLowerCase())
				) || [];

			//Sort according to search string
			if (searchedOption.length > 0) {
				const sortedOptions = searchedOption.sort((a, b) => {
					const aLower = a.value.toLowerCase();
					const bLower = b.value.toLowerCase();
					const searchStringLower = searchString.toLowerCase();

					const indexOfA = aLower.indexOf(searchStringLower);
					const indexOfB = bLower.indexOf(searchStringLower);

					if (indexOfA === indexOfB) {
						return aLower.localeCompare(bLower);
					}

					return indexOfA - indexOfB;
				});

				setListOfOptions(sortedOptions);
				setAnchorEl(buttonRef.current);
			}
		} else {
			setListOfOptions(optionsCloned);
		}
	}, [searchString]);

	const [popWidth, setPopWidth] = React.useState(0);

	React.useEffect(() => {
		if (!buttonRef.current) return;

		const obs = new ResizeObserver((e) => {
			setPopWidth(e[0].borderBoxSize[0].inlineSize);
		});
		obs.observe(buttonRef.current);

		return () => {
			buttonRef.current && obs.unobserve(buttonRef.current);
		};
	}, []);

	return (
		<div>
			<Grid width={"100%"} ref={buttonRef}>
				<CustomInput
					placeholder={placeholder}
					name={name}
					value={valueString}
					onChange={handleClick}
					hasError={hasError || false}
					disableField={isDisabled}
					errorMessage={errorMessage || ""}
				/>
			</Grid>
			{listOfOptions.length > 0 && (
				<Popper
					placement={"bottom-end" || "top-start"}
					id={id}
					open={open}
					anchorEl={anchorEl}
					sx={{
						width: popWidth ? popWidth : "inherit",
						zIndex: "1500",
						maxHeight: "200px !important",
					}}
				>
					<List
						onClick={boxClick}
						sx={{
							width: "100%",
							height: "fitContent",
							maxHeight: "300px !important ",
							overflow: "scroll",
							overflowX: "hidden",
							maxWidth: width ? width : "100%",
							border: `1px solid ${theme.palette.grey[500]}`,
							bgcolor: "background.paper",
							borderRadius: "20px",
						}}
						aria-label="contacts"
					>
						{listOfOptions.map((item, i) => (
							<ListItem
								sx={{ width: width ? width : "100%", cursor: "pointer" }}
								key={i}
								onClick={(event) => handleMenuItemClick(event, item)}
							>
								<ListItemText primary={item.value} />
							</ListItem>
						))}
					</List>
				</Popper>
			)}
		</div>
	);
}
