import React, { useEffect, useState } from "react";

import { Tooltip } from "@mui/material";
import Box from "@mui/material/Box";
import Slider from "@mui/material/Slider";
import Typography from "@mui/material/Typography";

interface TrackFalseSliderProps {
  onChange: (values: number[]) => void;
  title?: string;
  initialSliderValues: number[];
  min?: number;
  max?: number;
  step?: number;
}

// Custom Tooltip component
const ValueLabelComponent = (props: { children: React.ReactElement; open: boolean; value: number }) => {
  const { children, open, value } = props;

  return (
    <Tooltip
      open={open}
      placement="top"
      title={value}
      componentsProps={{
        tooltip: {
          sx: {
            backgroundColor: "white",
            color: "black",
            fontSize: "0.875rem",
            padding: "4px 8px",
            borderRadius: "4px",
            height: "30px",
            border: "1px solid #E8EBEC",
            boxShadow: "0px 2px 6px rgba(0, 0, 0, 0.2)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          },
        },
      }}
    >
      {children}
    </Tooltip>
  );
};

// Update function signature
export default function TrackFalseSlider({
  onChange = () => {},
  title = "Range Slider",
  initialSliderValues = [],
  min = 0,
  max = 200,
  step = 1,
}: TrackFalseSliderProps) {
  const [sliderValues, setSliderValues] = useState<number[]>(initialSliderValues || []);

  useEffect(() => {
    setSliderValues(initialSliderValues);
  }, [initialSliderValues]);

  const handleSliderChange = (_event: Event, newValue: number[]) => {
    setSliderValues(newValue);
    onChange(newValue);
  };

  const getRailSections = () => {
    const sortedValues = [...sliderValues].sort((a, b) => a - b);
    const colors = ["#EA483F", "#FFA07D", "#7FD167", "#FFA07D", "#EA483F"];
    const sections = [];

    // Color before the first pointer
    if (sortedValues[0] > min) {
      sections.push({
        left: "0%",
        width: `${((sortedValues[0] - min) / (max - min)) * 100}%`,
        backgroundColor: colors[0],
      });
    }

    for (let i = 0; i < sortedValues.length - 1; i++) {
      sections.push({
        left: `${((sortedValues[i] - min) / (max - min)) * 100}%`,
        width: `${((sortedValues[i + 1] - sortedValues[i]) / (max - min)) * 100}%`,
        backgroundColor: colors[i + 1],
      });
    }

    // Color after the last pointer
    if (sortedValues[sortedValues.length - 1] < max) {
      sections.push({
        left: `${((sortedValues[sortedValues.length - 1] - min) / (max - min)) * 100}%`,
        width: `${((max - sortedValues[sortedValues.length - 1]) / (max - min)) * 100}%`,
        backgroundColor: colors[colors.length - 1],
      });
    }

    return sections;
  };

  return (
    <Box sx={{ width: "100%", position: "relative", paddingTop: "20px" }}>
      <Typography mb={3} id="track-false-range-slider" gutterBottom>
        {title}
      </Typography>
      <Box sx={{ position: "relative", height: "8px", backgroundColor: "lightgray", borderRadius: "4px", top: "20px" }}>
        {getRailSections().map((section, index) => (
          <Box
            key={index}
            sx={{
              position: "absolute",
              height: "100%",
              borderRadius: "4px",
              ...section,
            }}
          />
        ))}
      </Box>
      <Slider
        step={step}
        track={false}
        aria-labelledby="track-false-range-slider"
        value={sliderValues}
        onChange={(event: Event, value: number | number[]) => handleSliderChange(event, value as number[])}
        valueLabelDisplay="on"
        min={min}
        max={max}
        components={{
          ValueLabel: ValueLabelComponent,
        }}
        sx={{
          "& .MuiSlider-thumb": {
            width: "16px",
            height: "16px",
            backgroundColor: "teal",
            "&:before": {
              boxShadow: "0 2px 4px rgba(185, 38, 38, 0.3)",
            },
          },
          "& .MuiSlider-rail": {
            display: "none",
          },
        }}
      />
    </Box>
  );
}
