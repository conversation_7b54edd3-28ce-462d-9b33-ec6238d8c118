export const emailRegexErrorMsg = "Invalid email address";
export const emailRequiredErrorMsg = "Email is required";
export const firstNameRequiredErrorMsg = "First Name is required";
export const firstNameOrSurnameRegexErrorMsg = "Must contain only letters, spaces, hyphens, or diacritic characters";
export const genderRequiredErrorMessage = "Gender is required";
export const lastNameRequiredErrorMsg = "Last Name is required";
export const npiRequiredErrorMsg = "NPI is required";
export const npiRegexErrorMsg = "Must be 10 digits";
export const phoneRequiredErrorMsg = "Phone is required";
export const phoneRegexErrorMsg = "Invalid phone number. It must be 10 digits.";
export const addressLine1RequiredErrorMsg = "Line 1 is required";
export const addressLine1Max128ErrorMsg = "Address must be less than 128 characters";
export const cityRequiredErrorMsg = "City field is required";
export const cityStateRegexErrorMsg = "Can only contain letters, numbers, spaces, hyphens (-), and periods (.)";
export const cityMax64ErrorMsg = "City must be less than 64 characters";
export const stateRequiredErrorMsg = "State field is required";
export const lessThan255ErrorMsg = "Must be less than 255 characters";
export const zipCodeRequiredErrorMsg = "Zip code is required";
export const zipCodeRegexErrorMsg = "Must be 5 or 9 digits. e.g.25256, 25256-2589";
export const countryRequiredErrorMsg = "Country field is required";
export const licenseStatesRequiredErrorMsg = "At least one license is required";
export const staffRoleRequiredErrorMsg = "Staff type is required";
export const locationRequiredErrorMsg = "Location is required";
export const nameRequiredErrorMsg = "Name is required";
export const subdomainRequiredErrorMsg = "Sub domain field is required";
export const ehrRequiredErrorMsg = "EHR is required";
export const practiceIdRequiredErrorMsg = "Practice ID is required";
export const providerTypeRequiredErrorMsg = "Provider type is required";
export const mrnRegexErrorMsg = "MRN must be 10 digits";
export const providerRequiredErrorMsg = "Provider is required";
export const birthDateRequiredErrorMsg = "Date of birth is required";
export const nurseRequiredErrorMsg = "Nurse is required";
export const documentNameRequiredErrorMsg = "Document name is required";
export const passwordRegexErrorMsg =
  "Password must be 8+ characters, with at least one uppercase, one lowercase, one number, and one special character. No spaces.";
export const newPasswordRequiredErrorMsg = "New Password is required";
export const passwordIsRequired = "Password is required";
export const otpRequiredErrorMsg = "OTP is required";
export const otpMax6DigitErrorMsg = "OTP must be at least 6 digits";
export const otpRegexErrorMsg = "OTP must only contain digits";
export const passwordMustMatchErrorMsg = "Passwords must match";
export const confirmNewPaswordErrorMsg = "Confirm new password is required";
export const licenseNumberReqiredErrorMsg = "License number is required";
export const licenseExpiryDateRequiredErrorMsg = "Expiry date is required";
export const licenseStateReqiredErrorMsg = "Licensed state is required";
export const messageRequiredErrorMsg = "Message is required";
export const subjectRequiredErrorMsg = "Subject is required";
