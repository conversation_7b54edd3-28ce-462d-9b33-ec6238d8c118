export type StringMap = {
  [key: string]: string;
};

export type StringMap2 = {
  [key: string]: string | boolean | number;
};

export const Roles = {
  SUPER_ADMIN: "SUPER_ADMIN",
  FRONTDESK: "FRONTDESK",
  BILLER: "BILLER",
  ENB: "ENB",
  PSYCHIATRIST: "PSYCHIATRIST",
  THERAPIST: "THERAPIST",
  NURSE: "NURSE",
  PATIENT: "PATIENT",
  ANONYMOUS: "ANONYMOUS",
  PROVIDER_GROUP_ADMIN: "PROVIDER_GROUP_ADMIN",
  SITE_ADMIN: "SITE_ADMIN",
  STAFF: "STAFF",
  PROVIDER: "PROVIDER",
};

export const RolesPortalMap: StringMap = {
  PROVIDER_GROUP_ADMIN: "admin",
  SUPER_ADMIN: "admin",
  FRONTDESK: "admin",
  BILLER: "provider",
  ENB: "provider",
  PSYCHIATRIST: "provider",
  THERAPIST: "provider",
  NURSE: "provider",
  PATIENT: "patient",
};

export const RoleType = {
  STAFF: "STAFF",
  PROVIDER: "PROVIDER",
  PATIENT: "PATIENT",
};

export const RolesOfUsers = [
  { value: Roles.FRONTDESK, label: "Front desk" },
  { value: Roles.BILLER, label: "Biller" },
  { value: Roles.SUPER_ADMIN, label: "Super Admin" },
  { value: Roles.PROVIDER_GROUP_ADMIN, label: "Provider Group Admin" },
  { value: Roles.SITE_ADMIN, label: "Site Admin" },
];

export const RolesOfAdminUsers = [
  { value: Roles.SUPER_ADMIN, label: "Super Admin" },
  { value: Roles.FRONTDESK, label: "Front desk" },
  { value: Roles.BILLER, label: "Biller" },
  // { value: Roles.PROVIDER_GROUP_ADMIN, label: "Provider Group Admin" },
];

export const RolesOfPGUsers = [
  { value: Roles.PROVIDER_GROUP_ADMIN, label: "Provider Group Admin" },
  { value: Roles.SITE_ADMIN, label: "Site Admin" },
  { value: Roles.BILLER, label: "Biller" },
];

export const Gender = [
  { value: "MALE", label: "Male" },
  { value: "FEMALE", label: "Female" },
  { value: "OTHER", label: "Other" },
];
