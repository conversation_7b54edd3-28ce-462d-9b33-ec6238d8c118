import { ALL_STATES_RAW_LIST } from "./states";

import StateList from "../assets/states.json";

export const getCitiesFromStateIsoCode = (stateIsoCode: string) => {
	const state = StateList.find((item) => item.code === stateIsoCode);
	if (state) {
		return state.cities.map((city) => {
			return {
				key: city,
				value: city,
			};
		});
	} else {
		return [];
	}
};

export const getCitiesFromStateName = (stateName: string) => {
	const selectedStateCode =
		ALL_STATES_RAW_LIST.find((state) => state.name === stateName)?.isoCode ||
		"";
	return getCitiesFromStateIsoCode(selectedStateCode);
};
