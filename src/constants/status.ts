import { alpha } from "@mui/material";
import { theme } from "../utils/theme";

export const Status = {
	ACTIVE: "ACTIVE",
	INACTIVE: "INACTIVE",
	ENABLE: "ENABLE",
	ENABLED: "ENABLED",
	DISABLE: "DISABLE",
	DISABLED: "DISABLED",
	READY: "READY",
	PENDING: "PENDING",
	APPROVED: "APPROVED",
	REJECTED: "REJECTED",
	CANCELLED: "CANCELLED",
	COMPLETED: "COMPLETED",
	DISCARDED: "DISCARDED",
	RESCHEDULED: "RESCHEDULED",
	NO_SHOW: "NO_SHOW",
	SCHEDULED: "SCHEDULED",
	CHECKED_IN: "CHECKED_IN",
	IN_EXAM: "IN_EXAM",
	CHECK_IN: "CHECK_IN",
	CONFIRMED: "CONFIRMED",
	PAY_NOW: "PAY_NOW",
	CONFIRMATION_PENDING: "CONFIRMATION_PENDING",
	INTAKE_PENDING: "INTAKE_PENDING",
	SCREENERS_PENDING: "SCREENERS_PENDING",
	PAYMENT_PENDING: "PAYMENT_PENDING",
	ABLE_TO_CHECK_IN: "ABLE_TO_CHECK_IN",
	FEES_NOT_SET: "FEES_NOT_SET",
	NO_SHOW_FROM_PROVIDER: "NO_SHOW_FROM_PROVIDER",
	SIGN_OFF_PENDING: "SIGN_OFF_PENDING",
	HIGH: "HIGH",
	MEDIUM: "MEDIUM",
	LOW: "LOW",
	ACTIVE_COVERAGE: "Active Coverage",
	YES: "YES",
	NO: "NO",
	INITIATED: "INITIATED",
	SIGN_OFF_REQUIRED: "SIGN_OFF_REQUIRED",
	READY_FOR_BILL: "READY_FOR_BILL",
	READY_FOR_PAYMENT: "READY_FOR_PAYMENT",
	READY_FOR_CLAIM: "READY_FOR_CLAIM",
	READY_FOR_SELF_PAY: "READY_FOR_SELF_PAY",
	CLAIM_IN_PROGRESS: "CLAIM_IN_PROGRESS",
	HOLD: "HOLD",
	SETTLED: "SETTLED",
	SIGNED_OFF: "SIGNED_OFF",
};

export const StatusColorMap: { [key: string]: string } = {
	ACTIVE: theme.palette.success.light,
	INACTIVE: theme.palette.error.light,
	ENABLE: theme.palette.success.main,
	ENABLED: theme.palette.success.main,
	DISABLE: theme.palette.error.light,
	DISABLED: theme.palette.error.light,
	READY: theme.palette.success.light,
	PENDING: theme.palette.warning.light,
	APPROVED: theme.palette.success.main,
	REJECTED: theme.palette.error.light,
	CANCELLED: theme.palette.error.light,
	COMPLETED: theme.palette.success.main,
	DISCARDED: alpha(theme.palette.primary.main, 0.5),
	RESCHEDULED: theme.palette.info.main,
	NO_SHOW: alpha(theme.palette.common.black, 0.5),
	SCHEDULED: theme.palette.info.main,
	CHECKED_IN: theme.palette.success.main,
	IN_EXAM: theme.palette.warning.light,
	CHECK_IN: theme.palette.success.main,
	CONFIRMED: theme.palette.success.main,
	PAY_NOW: theme.palette.success.main,
	CONFIRMATION_PENDING: theme.palette.warning.light,
	INTAKE_PENDING: theme.palette.warning.light,
	SCREENERS_PENDING: theme.palette.warning.light,
	PAYMENT_PENDING: theme.palette.warning.light,
	ABLE_TO_CHECK_IN: theme.palette.success.main,
	FEES_NOT_SET: theme.palette.warning.light,
	NO_SHOW_FROM_PROVIDER: alpha(theme.palette.common.black, 0.5),
	SIGN_OFF_PENDING: theme.palette.warning.light,
	HIGH: theme.palette.error.light,
	MEDIUM: theme.palette.info.main,
	ACTIVE_COVERAGE: theme.palette.success.main,
	LOW: theme.palette.warning.light,
	YES: theme.palette.primary.light,
	NO: theme.palette.primary.light,
	INITIATED: theme.palette.primary.light,
	SIGN_OFF_REQUIRED: theme.palette.error.dark,
	READY_FOR_BILL: theme.palette.success.main,
	READY_FOR_PAYMENT: theme.palette.primary.light,
	READY_FOR_CLAIM: theme.palette.success.main,
	READY_FOR_SELF_PAY: theme.palette.warning.light,
	CLAIM_IN_PROGRESS: theme.palette.warning.light,
	HOLD: theme.palette.primary.light,
	SETTLED: theme.palette.success.main,
	SIGNED_OFF: theme.palette.success.main,
	IN_PROGRESS: theme.palette.warning.light,
};
