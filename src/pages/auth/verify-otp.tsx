import { useEffect, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";

import { Button, Grid2 as Grid, <PERSON>, Typography } from "@mui/material";

import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

import CustomLabel from "@/common-components/custom-label/custom-label";
import CustomOtp from "@/common-components/custom-otp/custom-otp";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import OtpVerifySVG from "@/assets/image_svg/auth/otp-verify.svg";
import AuthLayout from "@/components/layouts/AuthLayout";
import { otpMax6DigitErrorMsg, otpRegexErrorMsg, otpRequiredErrorMsg } from "@/constants/error-messages";
import { ResetLinkType } from "@/models/auth/reset-linktype";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useUserControllerServiceResendOtp, useUserControllerServiceVerifyOtp } from "@/sdk/queries";
import { calculateTimeRemaining, formatCountdown } from "@/utils/otp-countdown";
import { theme } from "@/utils/theme";

export const setPasswordSchema = yup.object().shape({
  otp: yup
    .string()
    .required(otpRequiredErrorMsg)
    .min(6, otpMax6DigitErrorMsg)
    .matches(/^[0-9]+$/, otpRegexErrorMsg),
});

const VerifyOtpPage = () => {
  const initialValues = {
    otp: "",
  };

  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const hasInitialSendOtp = useRef(false);

  const isForgotPassword = Boolean(location?.state?.isForgotPassword);
  const [otp, setOtp] = useState("");
  const [countdown, setCountdown] = useState<number>(0);

  const { minutes, seconds } = formatCountdown(countdown);

  const { mutateAsync: verifyOtpMutateAsync, isPending: isVerifyingOtp } = useUserControllerServiceVerifyOtp({
    onError: (error) => {
      const message = (error && (error as ErrorResponseEntity)?.body?.message) || "Error occurred while verifying OTP";

      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: message as string,
        })
      );
    },
    onSuccess: (data) => {
      if (data.data) {
        navigate("/auth/set-password", {
          state: {
            emailVal: location.state ? location.state?.email : "",
            otpVal: otp,
            isForgotPassword: isForgotPassword,
          },
        });
      } else {
        dispatch(
          setSnackbarOn({
            severity: AlertSeverity.ERROR,
            message: "Invalid OTP. Please try again.",
          })
        );
      }
    },
  });

  const { mutateAsync: resendOtpMutateAsync, isSuccess: isSuccessResendOtp } = useUserControllerServiceResendOtp({
    onError: (error) => {
      const message = (error && (error as ErrorResponseEntity)?.body?.message) || "Error occurred while resending OTP";

      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: message as string,
        })
      );
    },
    onSuccess: () => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message: "Please check your email for the OTP code.",
        })
      );
    },
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(setPasswordSchema),
  });

  useEffect(() => {
    if (!location.state?.initialSendOtp && !hasInitialSendOtp.current) {
      localStorage.removeItem("otpResendTimestamp");
      handleResetOtp();
      hasInitialSendOtp.current = true;
      navigate(location.pathname, {
        state: { ...location.state, initialSendOtp: true },
        replace: true,
      });
    } else {
      setCountdown(calculateTimeRemaining(localStorage.getItem("otpResendTimestamp")));
    }
  }, []);

  useEffect(() => {
    if (countdown > 0) {
      const timestamp = localStorage.getItem("otpResendTimestamp") ?? Date.now().toString();

      const timer = setInterval(() => {
        if (timestamp) {
          const diff = calculateTimeRemaining(timestamp);
          setCountdown(diff);
          if (diff === 0) {
            localStorage.removeItem("otpResendTimestamp");
          }
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [isSuccessResendOtp]);

  const onSubmit = async (values: unknown) => {
    await verifyOtpMutateAsync({
      linkType: isForgotPassword ? ResetLinkType.RESET : ResetLinkType.SET,
      email: location.state?.email,
      otp: (values as { otp: string }).otp,
    });
  };

  const handleResetOtp = async () => {
    await resendOtpMutateAsync({
      email: location.state ? location.state?.email : "",
      linkType: isForgotPassword ? ResetLinkType.RESET : ResetLinkType.SET,
    });
  };

  return (
    <AuthLayout illustrationSrc={OtpVerifySVG}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid display="flex" flexDirection="column" gap="8px" paddingBottom="24px">
          <Typography fontWeight={600} fontSize="30px">
            OTP Verification
          </Typography>
          <Typography fontSize="16px" fontWeight={400} color={theme.palette.text.secondary}>
            Check the code in the {isForgotPassword ? "password reset" : "invitation"} email sent{" "}
            <Typography>{location.state["email"]}</Typography>
          </Typography>
        </Grid>

        <Grid display="flex" flexDirection="column" gap="24px">
          <Grid>
            <CustomLabel label="Input Code" />
            <Controller
              control={control}
              name="otp"
              render={({ field }) => (
                <CustomOtp
                  {...field}
                  onChange={function (otp: string): void {
                    setOtp(otp);
                    setValue("otp", otp, { shouldValidate: true });
                  }}
                  value={otp}
                  hasError={!!errors.otp}
                  errorMessage={errors.otp?.message || ""}
                />
              )}
            />
          </Grid>

          <Grid>
            <Button variant="contained" fullWidth type="submit" loading={isVerifyingOtp}>
              Verify OTP
            </Button>
          </Grid>

          <Grid>
            <Typography variant="bodyMedium" color="#717C7E">
              If you didn&apos;t receive the code?&nbsp;
              {countdown > 0 ? (
                `Resend OTP in ${minutes}:${seconds.toString().padStart(2, "0")}`
              ) : (
                <Link
                  onClick={() => {
                    handleResetOtp();
                    const timeNow = Date.now().toString();

                    localStorage.setItem("otpResendTimestamp", timeNow);
                    setCountdown(calculateTimeRemaining(timeNow));
                  }}
                >
                  Resend
                </Link>
              )}
            </Typography>
          </Grid>
        </Grid>
      </form>
    </AuthLayout>
  );
};

export default VerifyOtpPage;
