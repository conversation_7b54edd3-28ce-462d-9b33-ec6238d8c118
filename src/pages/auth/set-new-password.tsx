import { Controller, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";

import { Button, Typography } from "@mui/material";
import { Grid } from "@mui/system";

import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

import CustomInput from "@/common-components/custom-input/custom-input";
import CustomLabel from "@/common-components/custom-label/custom-label";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import SetPasswordSVG from "@/assets/image_svg/auth/set_password.svg";
import AuthLayout from "@/components/layouts/AuthLayout";
import {
  confirmNewPaswordErrorMsg,
  newPasswordRequiredErrorMsg,
  passwordMustMatchErrorMsg,
  passwordRegexErrorMsg,
} from "@/constants/error-messages";
import { ResetLinkType } from "@/models/auth/reset-linktype";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { useUserControllerServiceSetPassword } from "@/sdk/queries";
import { passwordRegx } from "@/utils/regex";

export const setPasswordSchema = yup.object().shape({
  newPassword: yup.string().required(newPasswordRequiredErrorMsg).matches(passwordRegx, passwordRegexErrorMsg),
  confirmNewPassword: yup
    .string()
    .oneOf([yup.ref("newPassword") as unknown as string], passwordMustMatchErrorMsg)
    .required(confirmNewPaswordErrorMsg),
});

const SetPasswordPage = () => {
  const initialValues = {
    newPassword: "",
    confirmNewPassword: "",
  };

  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const isForgotPassword = location.state?.isForgotPassword;

  const { mutateAsync: setPasswordMutateAsync, isPending: isSettingPassword } = useUserControllerServiceSetPassword({
    onError: (error) => {
      const message =
        (error && (error as ErrorResponseEntity)?.body?.message) || "Error occurred while setting password";

      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: message as string,
        })
      );
    },
    onSuccess: (data) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message: (data?.message || `Password ${isForgotPassword ? "reset" : "set"} successsfully`) as string,
        })
      );
      navigate("/auth/login", {
        state: { email: location?.state?.emailVal ?? "" },
      });
    },
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: initialValues,
    resolver: yupResolver(setPasswordSchema),
  });

  const onSubmit = async (values: typeof initialValues) => {
    await setPasswordMutateAsync({
      linkType: location?.state?.forgetPassword ? ResetLinkType.RESET : ResetLinkType.SET,
      requestBody: {
        newPassword: values.newPassword,
        email: location.state ? location.state?.emailVal : "",
        otp: location.state ? location.state?.otpVal : "",
      },
    });
  };

  return (
    <AuthLayout illustrationSrc={SetPasswordSVG}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid display="flex" flexDirection="column" gap="8px" paddingBottom="24px">
          <Typography fontWeight={600} fontSize="30px">
            {isForgotPassword ? "Reset New Password" : "Set New Password"}
          </Typography>
        </Grid>

        <Grid display="flex" flexDirection="column" gap="24px">
          <Grid>
            <CustomLabel label="New Password" />

            <Controller
              control={control}
              name="newPassword"
              render={({ field }) => (
                <CustomInput
                  placeholder={"Enter Your Password"}
                  {...field}
                  hasError={!!errors.newPassword}
                  errorMessage={errors.newPassword?.message}
                  onChange={(event) => {
                    setValue("newPassword", event.target.value, {
                      shouldValidate: true,
                    });
                  }}
                  isPassword
                />
              )}
            />
          </Grid>

          <Grid>
            <CustomLabel label="Confirm Password" />

            <Controller
              control={control}
              name="confirmNewPassword"
              render={({ field }) => (
                <CustomInput
                  placeholder={"Confirm Your Password"}
                  {...field}
                  hasError={!!errors.confirmNewPassword}
                  errorMessage={errors.confirmNewPassword?.message}
                  onChange={(event) => {
                    setValue("confirmNewPassword", event.target.value, {
                      shouldValidate: true,
                    });
                  }}
                  isPassword
                />
              )}
            />
          </Grid>

          <Grid>
            <Button variant="contained" fullWidth type="submit" disabled={isSettingPassword}>
              {isForgotPassword ? "Reset Password" : "Set Password"}
            </Button>
          </Grid>
        </Grid>
      </form>
    </AuthLayout>
  );
};

export default SetPasswordPage;
