import { Grid } from "@mui/system";
// import { useDispatch } from "react-redux";
// import { AlertSeverity } from "../../../common-components/snackbar-alert/snackbar-alert";
import ProviderGroupList from "../../../components/admin-portal/provider-group/provider-group-list";
// import { setSnackbarOn } from "../../../redux/actions/snackbar-action";

const ProviderGroupPage = () => {
	// const dispatch = useDispatch();

	// const check = () => {
	// 	dispatch(
	// 		setSnackbarOn({
	// 			severity: AlertSeverity.SUCCESS,
	// 			message: "Successfully updated list",
	// 			subMessage: "Your changes have been saved. ",
	// 			// arrayOfBtns: [
	// 			// 	{
	// 			// 		color: `${theme.palette.common.black}`,
	// 			// 		fontWeight: 5,
	// 			// 		// onClick: handleClick,
	// 			// 		title: "Dismiss",
	// 			// 	},
	// 			// 	{
	// 			// 		color: `${theme.palette.common.black}`,
	// 			// 		fontWeight: 5,
	// 			// 		// onClick: handleClick,
	// 			// 		title: "View Changes",
	// 			// 	},
	// 			// ],
	// 		})
	// 	);
	// 	// dispatch(
	// 	// 	setSnackbarOn({
	// 	// 		severity: AlertSeverity.ERROR,
	// 	// 		message: "ICD10 code added succesully",
	// 	// 	})
	// 	// );
	// 	// dispatch(
	// 	// 	setSnackbarOn({
	// 	// 		severity: AlertSeverity.INFO,
	// 	// 		message: "ICD10 code added succesully",
	// 	// 	})
	// 	// );
	// };

	// check();

	return (
		<Grid width={"100%"}>
			<ProviderGroupList />
		</Grid>
	);
};

export default ProviderGroupPage;
