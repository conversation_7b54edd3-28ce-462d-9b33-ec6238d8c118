import { createAsyncThunk } from "@reduxjs/toolkit";
import { Provider, UserControllerService } from "../../sdk/requests";

export const getProfileDate1 = createAsyncThunk<Provider , void , {rejectValue: string}>("profile/getProfile" , async(_,{rejectWithValue})=>{
    try{
        const res = await UserControllerService.getProfile1()
        return res.data as Provider
    }catch(error){
        if (error instanceof Error) {
            return rejectWithValue(error.message);
          } else {
            return rejectWithValue("An unexpected error occurred");
          }
    }
}  )

