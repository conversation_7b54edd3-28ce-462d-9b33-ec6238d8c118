// This file is auto-generated by @hey-api/openapi-ts

export type Response = {
  date?: string;
  code?:
    | "INTERNAL_ERROR"
    | "ACCESS_DENIED"
    | "BAD_REQUEST"
    | "NOT_FOUND"
    | "CREATED"
    | "UNSUPPORTED_MEDIA_TYPE"
    | "OK"
    | "UNAUTHORIZED"
    | "SERVICE_UNAVAILABLE"
    | "ENTITY"
    | "DB_ERROR"
    | "IAM_ERROR"
    | "AWS_ERROR"
    | "USER_ARCHIVED"
    | "USER_UNARCHIVED"
    | "USER_NOT_EXIST"
    | "BILLING_INVOICE_STATEMENT"
    | "INVALID_CREDENTIALS"
    | "LOGIN_FAILED"
    | "INVALID_REFRESH_TOKEN"
    | "LOGOUT_RESPONSE"
    | "LOGOUT_FAILED"
    | "SET_PASSWORD_RESPONSE"
    | "INVALID_PASSWORD_LINK"
    | "INVALID_PASSWORD"
    | "INVALID_OLD_PASSWORD"
    | "RESET_PASSWORD_FAILED"
    | "CHANGE_PASSWORD_RESPONSE"
    | "VERIFY_PASSWORD_LINK_RESPONSE"
    | "FORGOT_PASSWORD_RESPONSE"
    | "RESEND_INVITE_EMAIL_RESPONSE"
    | "USER_CREATED"
    | "USER_NOT_FOUND"
    | "USER_EMAIL_NOT_FOUND"
    | "UPDATE_USER_PROFILE_RESPONSE"
    | "USER_ENABLED"
    | "USER_DISABLED"
    | "DUPLICATE_EMAIL_ERROR"
    | "CHANGE_AVATAR_RESPONSE"
    | "GENERATE_AVATAR_RESPONSE"
    | "AVATAR_VOICE_RESPONSE"
    | "USER_EMAIL_REQUIRED"
    | "USER_EMAIL_UPDATE_ERROR"
    | "INVALID_USER_ROLE"
    | "USER_EMAIL_VERIFIED"
    | "SIGNED_UP"
    | "USER_INACTIVE"
    | "EMPTY_EMAIL"
    | "MAIL_SENT_SUCCESSFULLY"
    | "RESEND_OTP"
    | "INVALID_OTP"
    | "PATIENT_ONBOARD_SUCCESSFULLY"
    | "PROVIDER_GROUP_CREATED"
    | "PROVIDER_GROUP_UPDATED"
    | "FAILED_UPDATE_PROVIDER_GROUP"
    | "PROVIDER_GROUP_ENABLED"
    | "PROVIDER_GROUP_DISABLED"
    | "PROVIDER_GROUP_ARCHIVED"
    | "PROVIDER_GROUP_UNARCHIVED"
    | "FCM_SAVED"
    | "FCM_DELETED"
    | "REALMS_DELETED"
    | "DEVICE_CREATED"
    | "DEVICE_UPDATED"
    | "DEVICE_ENABLED"
    | "DEVICE_DISABLED"
    | "DEVICE_ARCHIVED"
    | "DEVICE_UNARCHIVED"
    | "DEVICE_NOT_FOUND"
    | "ALERT_CREATED"
    | "ALERT_UPDATED"
    | "LOCATION_CREATED"
    | "LOCATION_UPDATED"
    | "LOCATION_NOT_FOUND"
    | "LOCATION_ENABLED"
    | "LOCATION_DISABLED"
    | "LOCATION_ARCHIVED"
    | "LOCATION_UNARCHIVED"
    | "DEPARTMENT_CREATED"
    | "DEPARTMENT_UPDATED"
    | "DEPARTMENT_ENABLED"
    | "DEPARTMENT_DISABLED"
    | "DEPARTMENT_ARCHIVED"
    | "DEPARTMENT_UNARCHIVED"
    | "PROVIDER_CREATED"
    | "PROVIDER_UPDATED"
    | "PROVIDER_ENABLED"
    | "PROVIDER_DISABLED"
    | "PROVIDER_ARCHIVED"
    | "PROVIDER_UNARCHIVED"
    | "NPI_ALREADY_EXIST"
    | "PROVIDER_STATUS_UPDATED"
    | "PROVIDER_ARCHIVE_STATUS_UPDATED"
    | "PROVIDER_ONBOARDING_STATUS_UPDATED"
    | "NURSE_CREATED"
    | "NURSE_UPDATED"
    | "NURSE_AVATAR_STATUS_UPDATED"
    | "PATIENT_CREATED"
    | "PATIENT_UPDATE"
    | "PATIENT_STATUS_UPDATED"
    | "PATIENT_ARCHIVE_STATUS_UPDATED"
    | "PATIENT_UNARCHIVE_STATUS_UPDATED"
    | "PATIENT_ENABLE_DISABLE_RESPONSE"
    | "FAILED_PATIENT_UPDATE"
    | "PATIENT_NOT_FOUND"
    | "USER_MRN_UPDATE_ERROR"
    | "PATIENT_DATA_FETCHED"
    | "PATIENT_DOCUMENT_REMOVED"
    | "UPLOAD_DOC_INVALID_ERROR"
    | "FAILED_PATIENT_STATUS_UPDATE"
    | "PATIENT_ARCHIVE"
    | "FAILED_PATIENT_ARCHIVE"
    | "INVALID_BIRTHDATE"
    | "FAILED_UPDATE_PATIENT_INSURANCE"
    | "ADDED_TO_WAITING_LIST"
    | "PATIENT_ENABLED"
    | "PATIENT_DISABLED"
    | "PATIENT_ARCHIVED"
    | "PATIENT_UNARCHIVED"
    | "TENANT_CONFIGURATION_CREATED"
    | "TENANT_CONFIGURATION_UPDATED"
    | "FEE_AMOUNT_ADDED"
    | "FEE_AMOUNT_UPDATED"
    | "FEE_AMOUNT_STATUS_CHANGED"
    | "FEE_AMOUNT_ALREADY_EXISTS"
    | "FEE_AMOUNT_NOT_FOUND"
    | "FEE_DELETED"
    | "INVALID_MEDICAL_CODE"
    | "INSURANCEPAYER_NOT_FOUND"
    | "INSURANCE_POLICY_NOT_FOUND"
    | "TOO_MANY_INSURANCES"
    | "INSURANCE_NOT_FOUND"
    | "SECONDARY_INSURANCE_NOT_FOUND"
    | "INSURANCETYPE_UPDATED"
    | "CANNOT_UPDATE_INSURANCETYPE"
    | "PATIENT_INSURANCE_UPDATE"
    | "SECONDARY_INSURANCE_REMOVED"
    | "DUPLICATE_INSURANCE_ERROR"
    | "ELIGIBILITY_NOT_ALLOWED"
    | "INTAKE_FORM_ADDED"
    | "INTAKE_FORM_UPDATED"
    | "PATIENT_DOCUMENT_UPLOADED"
    | "REASON_IS_MANDATORY"
    | "CHARGEABLE_IS_MANDATORY"
    | "INTAKE_NEEDED"
    | "CONSENT_FORM_TEMPLATE_ADDED"
    | "CONSENT_FORM_TEMPLATE_UPDATED"
    | "CONSENT_FORM_TEMPLATE_ARCHIVED"
    | "CONSENT_FORM_TEMPLATE_UNARCHIVED"
    | "CONSENT_FORM_TEMPLATE_ENABLE"
    | "CONSENT_FORM_TEMPLATE_DISABLED"
    | "CONSENT_FORM_TEMPLATE_NOT_FOUND"
    | "PATIENT_CONSENT_STATUS_UPDATED"
    | "CONSENT_ARCHIVE_STATUS"
    | "ADDED_AVAILABILITY"
    | "CONSULT_TIME_CONFLICT"
    | "BOOKED_APPOINTMENT_SLOT"
    | "SLOT_NOT_AVAILABLE"
    | "AVAILABILITY_NOT_FOUND"
    | "DAY_SLOT_NOT_FOUND"
    | "SLOT_NOT_FOUND"
    | "INVALID_TIME_DURATION"
    | "PAST_START_TIME"
    | "SLOT_ALREADY_BOOKED"
    | "APPOINTMENT_CREATED"
    | "APPOINTMENT_DATA_FETCHED"
    | "APPOINTMENT_NOT_FOUND"
    | "APPOINTMENT_UPDATED"
    | "APPOINTMENT_CONFIRMED"
    | "NEW_APPT_ALREADY_EXIST"
    | "NEW_APPT_NOT_EXIST"
    | "EXISTING_APPOINTMENT_NOT_FOUND"
    | "RESCHEDULE_NOT_ALLOWED"
    | "APPOINTMENT_RESCHEDULED"
    | "APPOINTMENT_RESCHEDULED_WITH_CHARGE"
    | "CHECKED_IN_NOT_ENABLED"
    | "FOLLOW_UP_APPT_ERROR"
    | "APPT_NOT_COMPLETE_ERROR"
    | "EXISTING_APPT_STATUS_ERROR"
    | "APPOINTMENT_FEE_SET"
    | "APPOINTMENT_FEE_ALREADY_PAID"
    | "NO_SHOW_NOT_ALLOWED"
    | "BILL_AMOUNT"
    | "CANNOT_COMPLETE_APPOINTMENT"
    | "APPOINTMENT_REQUEST_CREATED"
    | "APPOINTMENT_BROADCASTED_SUCCESSFULLY"
    | "APPOINTMENT_REVOKE_SUCCESSFULLY"
    | "SEND_MESSAGE_RESPONSE"
    | "EMAIL_SUBJECT_MANDATORY"
    | "PATIENT_PHONE_NOT_PRESENT"
    | "PATIENT_SCREENER_ADDED"
    | "PATIENTSCREENER_NOT_FOUND"
    | "PATIENT_SCORE_ADDED"
    | "PATIENT_SCREENER_ANSWER_UPDATED"
    | "INVALID_SCREENER_TYPE"
    | "INVALID_ANSWER_OPTION"
    | "INCOMPLETE_SCREENER_ANSWERS"
    | "PATIENT_SCREENER_SCORE_UPDATED"
    | "PATIENT_SCREENER_DATA_FETCHED"
    | "PAYMENT_CARD_ADDED"
    | "PAYMENT_CARD_ALREADY_EXISTS"
    | "PAYMENT_CHARGED"
    | "FINE_CHARGED"
    | "PAYMENT_CARD_NOT_FOUND"
    | "PAYMENT_ALREADY_DONE"
    | "PAYMENT_CARD_NOT_EXISTS"
    | "PAYMENT_AMOUNT_NOT_SET"
    | "PAYMENT_BILL_STATUS_ERROR"
    | "INVALID_CARD_EXPIRATION"
    | "NO_NOTIFICATIONS"
    | "NOTIFICATION_MARKED_AS_SEEN"
    | "NOTIFICATIONS_COUNT"
    | "PATIENT_CONSENT_FORMS_SIGNED"
    | "PATIENT_CONSENT_FORM_NOT_FOUND"
    | "TEXTMACRO_SHORTCUT_EXISTS"
    | "TEXTMACRO_ADDED"
    | "TEXTMACRO_DATA_FETCHED"
    | "TEXTMACRO_UPDATED"
    | "FAILED_UPDATE_TEXT_MACRO"
    | "FAILED_TEXTMACRO_ARCHIVE"
    | "TEXTMACRO_ARCHIVE"
    | "TEXTMACRO_RESTORED"
    | "FAILED_TEXTMACRO_RESTORE"
    | "TEXTMACRO_NOT_EXIST"
    | "TASK_ADDED"
    | "TASK_DATA_FETCHED"
    | "TASK_UPDATED"
    | "TASK_ARCHIVED"
    | "TASK_NOT_EXIST"
    | "FAILED_UPDATE_TASK"
    | "INVALID_TASK_STATUS"
    | "FAILED_TASK_ARCHIVE"
    | "INVALID_DUEDATE"
    | "FINE_CHARGED_APPOINTMENT"
    | "APPOINTMENT_FINE"
    | "CANNOT_CHARGE_FINE"
    | "PAYMENT_SUCCESSFUL"
    | "VITAL_ADDED"
    | "VITAL_UPDATED"
    | "VITAL_DATA_FETCHED"
    | "VITAL_NOT_FOUND"
    | "FAILED_VITAL_UPDATED"
    | "EMITTED_SUCCESSFULLY"
    | "WEBHOOK_SUCCESSFUL"
    | "BILL_UPDATED"
    | "CLAIM_ADDED"
    | "BOOK_APPOINTMENT_ERROR"
    | "CANCEL_APPOINTMENT_ERROR"
    | "NOT_IMPLEMENTED"
    | "COMMENT_ADDED_SUCCESSFULLY"
    | "COMMENT_UPDATED_SUCCESSFULLY"
    | "COMMENT_DELETED_SUCCESSFULLY"
    | "MEDICAL_CODE_ADDED_SUCCESSFULLY"
    | "MEDICAL_CODE_UPDATED_SUCCESSFULLY"
    | "PROVIDER_REVIEW_ADDED_SUCCESSFULLY"
    | "PROVIDER_REVIEW_STATUS_UPDATED_SUCCESSFULLY"
    | "CHECK_IN_STATUS"
    | "REFILL_RX_ADDED"
    | "REFILL_RX_UPDATED"
    | "ROI_ADDED"
    | "ROI_UPDATED"
    | "ROI_STATUS_UPDATED"
    | "ROI_NOT_FOUND"
    | "INTERNAL_SERVER_ERROR"
    | "FILE_UPLOADED"
    | "EMPTY_FILE"
    | "MEDICAL_CODES_CREATED"
    | "MEDICAL_CODES_UPDATED"
    | "MEDICAL_CODE_ENABLED"
    | "MEDICAL_CODE_DISABLED"
    | "MEDICAL_CODE_ARCHIVED"
    | "MEDICAL_CODE_UNARCHIVED"
    | "PATIENT_ALLERGY_CREATED"
    | "PATIENT_ALLERGY_UPDATED"
    | "PATIENT_ALLERGY_ENABLED"
    | "PATIENT_ALLERGY_DISABLED"
    | "PATIENT_ALLERGY_ARCHIVED"
    | "PATIENT_ALLERGY_UNARCHIVED"
    | "PATIENT_VACCINE_CREATED"
    | "PATIENT_VACCINE_UPDATED"
    | "PATIENT_VACCINE_ENABLED"
    | "PATIENT_VACCINE_DISABLED"
    | "PATIENT_VACCINE_ARCHIVED"
    | "PATIENT_VACCINE_UNARCHIVED"
    | "PATIENT_VITAL_CREATED"
    | "PATIENT_VITAL_UPDATED"
    | "STICKY_NOTE_CREATED"
    | "STICKY_NOTE_UPDATED"
    | "STICKY_NOTE_ENABLED"
    | "STICKY_NOTE_DISABLED"
    | "STICKY_NOTE_ARCHIVED"
    | "STICKY_NOTE_UNARCHIVED"
    | "PATIENT_MEDICATION_CREATED"
    | "PATIENT_MEDICATION_UPDATED"
    | "PATIENT_MEDICATION_ENABLE_DISABLE_RESPONSE"
    | "PATIENT_MEDICATION_ARCHIVE_STATUS_UPDATED"
    | "PATIENT_MEDICATION_ARCHIVED"
    | "PATIENT_MEDICATION_UNARCHIVED"
    | "PATIENT_MEDICAL_HISTORY_CREATED"
    | "PATIENT_MEDICAL_HISTORY_UPDATED"
    | "PATIENT_MEDICAL_HISTORY_ENABLE_DISABLE_RESPONSE"
    | "PATIENT_MEDICAL_HISTORY_ARCHIVE_STATUS_UPDATED"
    | "PATIENT_SURGICAL_HISTORY_CREATED"
    | "PATIENT_SURGICAL_HISTORY_UPDATED"
    | "PATIENT_SURGICAL_HISTORY_ENABLE_DISABLE_RESPONSE"
    | "PATIENT_SURGICAL_HISTORY_ARCHIVE_STATUS_UPDATED"
    | "PATIENT_FAMILY_HISTORY_CREATED"
    | "PATIENT_FAMILY_HISTORY_UPDATED"
    | "PATIENT_FAMILY_HISTORY_ENABLE_DISABLE_RESPONSE"
    | "PATIENT_FAMILY_HISTORY_ARCHIVE_STATUS_UPDATED"
    | "PATIENT_SOCIAL_HISTORY_CREATED"
    | "PATIENT_SOCIAL_HISTORY_UPDATED"
    | "PATIENT_SOCIAL_HISTORY_ENABLE_DISABLE_RESPONSE"
    | "PATIENT_SOCIAL_HISTORY_ARCHIVE_STATUS_UPDATED"
    | "PATIENT_MEDICATION_CREATED_SUCCESSFULLY"
    | "PATIENT_MEDICATION_UPDATED_SUCCESSFULLY"
    | "DOCUMENT_TAG_CREATED"
    | "DOCUMENT_TAG_UPDATED"
    | "DOCUMENT_TAG_ENABLED"
    | "DOCUMENT_TAG_DISABLED"
    | "DOCUMENT_TAG_ARCHIVED"
    | "DOCUMENT_TAG_UNARCHIVED"
    | "PATIENT_LAB_ORDER_CREATED"
    | "PATIENT_LAB_ORDER_UPDATED"
    | "PATIENT_LAB_ORDER_NOT_FOUND"
    | "PATIENT_LAB_ORDER_ENABLED"
    | "PATIENT_LAB_ORDER_DISABLED"
    | "PATIENT_LAB_ORDER_ARCHIVED"
    | "PATIENT_LAB_ORDER_UNARCHIVED"
    | "CLINICAL_NOTE_ALREADY_FOUND"
    | "CLINICAL_NOTE_CREATED"
    | "CLINICAL_NOTE_UPDATED"
    | "CLINICAL_NOTE_NOT_FOUND"
    | "CLINICAL_NOTE_ENABLED"
    | "CLINICAL_NOTE_DISABLED"
    | "CLINICAL_NOTE_ARCHIVED"
    | "CLINICAL_NOTE_UNARCHIVED"
    | "USER_DOCUMENT_UPLOAD"
    | "ROLE_ADDED"
    | "PRIVILEGE_NOT_FOUND"
    | "ROLE_UPDATED"
    | "ROLE_SYNC"
    | "ROLE_NOT_FOUND"
    | "CUSTOM_FORM_CREATED"
    | "CUSTOM_FORM_STATUS_UPDATED"
    | "CUSTOM_FORM_UPDATED"
    | "CUSTOM_FORM_ENABLED"
    | "CUSTOM_FORM_DISABLED"
    | "CUSTOM_FORM_ARCHIVED"
    | "CUSTOM_FORM_RESTORED"
    | "CUSTOM_FORM_COPY"
    | "CUSTOM_FORM_TEMPLATE_CREATED"
    | "CUSTOM_FORM_TEMPLATE_STATUS_UPDATED"
    | "CUSTOM_FORM_TEMPLATE_UPDATED"
    | "CUSTOM_FORM_TEMPLATE_ENABLED"
    | "CUSTOM_FORM_TEMPLATE_DISABLED"
    | "CUSTOM_FORM_TEMPLATE_ARCHIVED"
    | "CUSTOM_FORM_TEMPLATE_RESTORED"
    | "MICROS_FORM_CREATED"
    | "MICROS_FORM_UPDATED"
    | "MICROS_FORM_STATUS_UPDATED"
    | "MICROS_FORM_ARCHIVE_STATUS_UPDATED"
    | "VISIT_NOTE_FORM_CREATED"
    | "VISIT_NOTE_FORM_UPDATED"
    | "VISIT_NOTE_FORM_STATUS_UPDATED"
    | "VISIT_NOTE_FORM_ARCHIVE_STATUS_UPDATED"
    | "REVIEW_OF_SYSTEM_FORM_CREATED"
    | "REVIEW_OF_SYSTEM_FORM_UPDATED"
    | "REVIEW_OF_SYSTEM_FORM_STATUS_UPDATED"
    | "REVIEW_OF_SYSTEM_FORM_ARCHIVE_STATUS_UPDATED"
    | "CUSTOM_QUESTIONNAIRE_FORM_CREATED"
    | "CUSTOM_QUESTIONNAIRE_FORM_UPDATED"
    | "CUSTOM_QUESTIONNAIRE_FORM_STATUS_UPDATED"
    | "CUSTOM_QUESTIONNAIRE_FORM_ARCHIVE_STATUS_UPDATED"
    | "CARE_PLAN_CREATED"
    | "CARE_PLAN_UPDATED"
    | "CARE_PLAN_ENABLED"
    | "CARE_PLAN_DISABLED"
    | "CARE_PLAN_ARCHIVE_STATUS_UPDATED"
    | "CARE_PLAN_ARCHIVED"
    | "CARE_PLAN_UNARCHIVED"
    | "CARE_PLAN_ASSIGNED"
    | "PATIENT_CARE_PLAN_STATUS_UPDATED"
    | "VITAL_REFERENCE_RANGE_UPDATED"
    | "PATIENT_VITAL_REFERENCE_RANGE_UPDATED"
    | "ANNOTABLE_IMAGE_FORM_CREATED"
    | "ANNOTABLE_IMAGE_FORM_UPDATED"
    | "ANNOTABLE_IMAGE_FORM_STATUS_UPDATED"
    | "ANNOTABLE_IMAGE_FORM_ARCHIVE_STATUS_UPDATED"
    | "ORDER_SET_FORM_CREATED"
    | "ORDER_SET_FORM_UPDATED"
    | "ORDER_SET_FORM_STATUS_UPDATED"
    | "PHYSICAL_EXAM_FORM_CREATED"
    | "PHYSICAL_EXAM_FORM_UPDATED"
    | "PHYSICAL_EXAM_FORM_STATUS_UPDATED"
    | "PHYSICAL_EXAM_FORM_ARCHIVE_STATUS_UPDATED"
    | "PATIENT_DIAGNOSIS_CREATED"
    | "PATIENT_DIAGNOSIS_UPDATED"
    | "PATIENT_DIAGNOSIS_ARCHIVED"
    | "PATIENT_DIAGNOSIS_UNARCHIVED"
    | "PATIENT_DIAGNOSIS_ARCHIVE_STATUS"
    | "PATIENT_VITAL_CREATED_SUCCESSFULLY"
    | "PATIENT_VITAL_UPDATED_SUCCESSFULLY"
    | "PATIENT_VITAL_SETTING_UPDATED"
    | "CHECK_CONSULT_TIME"
    | "EVENT_EMITTED_SUCCESSFULLY"
    | "NOTIFICATION_SEND"
    | "TIME_LOG_CREATED"
    | "TIME_LOG_UPDATED"
    | "TIME_LOG_DELETED"
    | "PATIENT_TRAINED"
    | "AUTOMATIC_TIME_LOG_CREATED"
    | "NURSE_ACTIONS_STATISTIC_GENERATED"
    | "AUDIO_QUICK_SUMMARY_GENERATED"
    | "INVITATION_SEND_SUCCESSFULLY"
    | "EMAIL_SENT"
    | "SMS_SENT"
    | "GATEWAY_TIMEOUT"
    | "NURSE_REASSIGNED_SUCCESSFULLY"
    | "UPDATE_PROVIDER_GROUP_CONFIGURATION";
  message?: {
    [key: string]: unknown;
  };
  data?: {
    [key: string]: unknown;
  };
  errors?: {
    [key: string]: string;
  };
  path?: string;
  requestId?: string;
  version?: string;
};

export type code =
  | "INTERNAL_ERROR"
  | "ACCESS_DENIED"
  | "BAD_REQUEST"
  | "NOT_FOUND"
  | "CREATED"
  | "UNSUPPORTED_MEDIA_TYPE"
  | "OK"
  | "UNAUTHORIZED"
  | "SERVICE_UNAVAILABLE"
  | "ENTITY"
  | "DB_ERROR"
  | "IAM_ERROR"
  | "AWS_ERROR"
  | "USER_ARCHIVED"
  | "USER_UNARCHIVED"
  | "USER_NOT_EXIST"
  | "BILLING_INVOICE_STATEMENT"
  | "INVALID_CREDENTIALS"
  | "LOGIN_FAILED"
  | "INVALID_REFRESH_TOKEN"
  | "LOGOUT_RESPONSE"
  | "LOGOUT_FAILED"
  | "SET_PASSWORD_RESPONSE"
  | "INVALID_PASSWORD_LINK"
  | "INVALID_PASSWORD"
  | "INVALID_OLD_PASSWORD"
  | "RESET_PASSWORD_FAILED"
  | "CHANGE_PASSWORD_RESPONSE"
  | "VERIFY_PASSWORD_LINK_RESPONSE"
  | "FORGOT_PASSWORD_RESPONSE"
  | "RESEND_INVITE_EMAIL_RESPONSE"
  | "USER_CREATED"
  | "USER_NOT_FOUND"
  | "USER_EMAIL_NOT_FOUND"
  | "UPDATE_USER_PROFILE_RESPONSE"
  | "USER_ENABLED"
  | "USER_DISABLED"
  | "DUPLICATE_EMAIL_ERROR"
  | "CHANGE_AVATAR_RESPONSE"
  | "GENERATE_AVATAR_RESPONSE"
  | "AVATAR_VOICE_RESPONSE"
  | "USER_EMAIL_REQUIRED"
  | "USER_EMAIL_UPDATE_ERROR"
  | "INVALID_USER_ROLE"
  | "USER_EMAIL_VERIFIED"
  | "SIGNED_UP"
  | "USER_INACTIVE"
  | "EMPTY_EMAIL"
  | "MAIL_SENT_SUCCESSFULLY"
  | "RESEND_OTP"
  | "INVALID_OTP"
  | "PATIENT_ONBOARD_SUCCESSFULLY"
  | "PROVIDER_GROUP_CREATED"
  | "PROVIDER_GROUP_UPDATED"
  | "FAILED_UPDATE_PROVIDER_GROUP"
  | "PROVIDER_GROUP_ENABLED"
  | "PROVIDER_GROUP_DISABLED"
  | "PROVIDER_GROUP_ARCHIVED"
  | "PROVIDER_GROUP_UNARCHIVED"
  | "FCM_SAVED"
  | "FCM_DELETED"
  | "REALMS_DELETED"
  | "DEVICE_CREATED"
  | "DEVICE_UPDATED"
  | "DEVICE_ENABLED"
  | "DEVICE_DISABLED"
  | "DEVICE_ARCHIVED"
  | "DEVICE_UNARCHIVED"
  | "DEVICE_NOT_FOUND"
  | "ALERT_CREATED"
  | "ALERT_UPDATED"
  | "LOCATION_CREATED"
  | "LOCATION_UPDATED"
  | "LOCATION_NOT_FOUND"
  | "LOCATION_ENABLED"
  | "LOCATION_DISABLED"
  | "LOCATION_ARCHIVED"
  | "LOCATION_UNARCHIVED"
  | "DEPARTMENT_CREATED"
  | "DEPARTMENT_UPDATED"
  | "DEPARTMENT_ENABLED"
  | "DEPARTMENT_DISABLED"
  | "DEPARTMENT_ARCHIVED"
  | "DEPARTMENT_UNARCHIVED"
  | "PROVIDER_CREATED"
  | "PROVIDER_UPDATED"
  | "PROVIDER_ENABLED"
  | "PROVIDER_DISABLED"
  | "PROVIDER_ARCHIVED"
  | "PROVIDER_UNARCHIVED"
  | "NPI_ALREADY_EXIST"
  | "PROVIDER_STATUS_UPDATED"
  | "PROVIDER_ARCHIVE_STATUS_UPDATED"
  | "PROVIDER_ONBOARDING_STATUS_UPDATED"
  | "NURSE_CREATED"
  | "NURSE_UPDATED"
  | "NURSE_AVATAR_STATUS_UPDATED"
  | "PATIENT_CREATED"
  | "PATIENT_UPDATE"
  | "PATIENT_STATUS_UPDATED"
  | "PATIENT_ARCHIVE_STATUS_UPDATED"
  | "PATIENT_UNARCHIVE_STATUS_UPDATED"
  | "PATIENT_ENABLE_DISABLE_RESPONSE"
  | "FAILED_PATIENT_UPDATE"
  | "PATIENT_NOT_FOUND"
  | "USER_MRN_UPDATE_ERROR"
  | "PATIENT_DATA_FETCHED"
  | "PATIENT_DOCUMENT_REMOVED"
  | "UPLOAD_DOC_INVALID_ERROR"
  | "FAILED_PATIENT_STATUS_UPDATE"
  | "PATIENT_ARCHIVE"
  | "FAILED_PATIENT_ARCHIVE"
  | "INVALID_BIRTHDATE"
  | "FAILED_UPDATE_PATIENT_INSURANCE"
  | "ADDED_TO_WAITING_LIST"
  | "PATIENT_ENABLED"
  | "PATIENT_DISABLED"
  | "PATIENT_ARCHIVED"
  | "PATIENT_UNARCHIVED"
  | "TENANT_CONFIGURATION_CREATED"
  | "TENANT_CONFIGURATION_UPDATED"
  | "FEE_AMOUNT_ADDED"
  | "FEE_AMOUNT_UPDATED"
  | "FEE_AMOUNT_STATUS_CHANGED"
  | "FEE_AMOUNT_ALREADY_EXISTS"
  | "FEE_AMOUNT_NOT_FOUND"
  | "FEE_DELETED"
  | "INVALID_MEDICAL_CODE"
  | "INSURANCEPAYER_NOT_FOUND"
  | "INSURANCE_POLICY_NOT_FOUND"
  | "TOO_MANY_INSURANCES"
  | "INSURANCE_NOT_FOUND"
  | "SECONDARY_INSURANCE_NOT_FOUND"
  | "INSURANCETYPE_UPDATED"
  | "CANNOT_UPDATE_INSURANCETYPE"
  | "PATIENT_INSURANCE_UPDATE"
  | "SECONDARY_INSURANCE_REMOVED"
  | "DUPLICATE_INSURANCE_ERROR"
  | "ELIGIBILITY_NOT_ALLOWED"
  | "INTAKE_FORM_ADDED"
  | "INTAKE_FORM_UPDATED"
  | "PATIENT_DOCUMENT_UPLOADED"
  | "REASON_IS_MANDATORY"
  | "CHARGEABLE_IS_MANDATORY"
  | "INTAKE_NEEDED"
  | "CONSENT_FORM_TEMPLATE_ADDED"
  | "CONSENT_FORM_TEMPLATE_UPDATED"
  | "CONSENT_FORM_TEMPLATE_ARCHIVED"
  | "CONSENT_FORM_TEMPLATE_UNARCHIVED"
  | "CONSENT_FORM_TEMPLATE_ENABLE"
  | "CONSENT_FORM_TEMPLATE_DISABLED"
  | "CONSENT_FORM_TEMPLATE_NOT_FOUND"
  | "PATIENT_CONSENT_STATUS_UPDATED"
  | "CONSENT_ARCHIVE_STATUS"
  | "ADDED_AVAILABILITY"
  | "CONSULT_TIME_CONFLICT"
  | "BOOKED_APPOINTMENT_SLOT"
  | "SLOT_NOT_AVAILABLE"
  | "AVAILABILITY_NOT_FOUND"
  | "DAY_SLOT_NOT_FOUND"
  | "SLOT_NOT_FOUND"
  | "INVALID_TIME_DURATION"
  | "PAST_START_TIME"
  | "SLOT_ALREADY_BOOKED"
  | "APPOINTMENT_CREATED"
  | "APPOINTMENT_DATA_FETCHED"
  | "APPOINTMENT_NOT_FOUND"
  | "APPOINTMENT_UPDATED"
  | "APPOINTMENT_CONFIRMED"
  | "NEW_APPT_ALREADY_EXIST"
  | "NEW_APPT_NOT_EXIST"
  | "EXISTING_APPOINTMENT_NOT_FOUND"
  | "RESCHEDULE_NOT_ALLOWED"
  | "APPOINTMENT_RESCHEDULED"
  | "APPOINTMENT_RESCHEDULED_WITH_CHARGE"
  | "CHECKED_IN_NOT_ENABLED"
  | "FOLLOW_UP_APPT_ERROR"
  | "APPT_NOT_COMPLETE_ERROR"
  | "EXISTING_APPT_STATUS_ERROR"
  | "APPOINTMENT_FEE_SET"
  | "APPOINTMENT_FEE_ALREADY_PAID"
  | "NO_SHOW_NOT_ALLOWED"
  | "BILL_AMOUNT"
  | "CANNOT_COMPLETE_APPOINTMENT"
  | "APPOINTMENT_REQUEST_CREATED"
  | "APPOINTMENT_BROADCASTED_SUCCESSFULLY"
  | "APPOINTMENT_REVOKE_SUCCESSFULLY"
  | "SEND_MESSAGE_RESPONSE"
  | "EMAIL_SUBJECT_MANDATORY"
  | "PATIENT_PHONE_NOT_PRESENT"
  | "PATIENT_SCREENER_ADDED"
  | "PATIENTSCREENER_NOT_FOUND"
  | "PATIENT_SCORE_ADDED"
  | "PATIENT_SCREENER_ANSWER_UPDATED"
  | "INVALID_SCREENER_TYPE"
  | "INVALID_ANSWER_OPTION"
  | "INCOMPLETE_SCREENER_ANSWERS"
  | "PATIENT_SCREENER_SCORE_UPDATED"
  | "PATIENT_SCREENER_DATA_FETCHED"
  | "PAYMENT_CARD_ADDED"
  | "PAYMENT_CARD_ALREADY_EXISTS"
  | "PAYMENT_CHARGED"
  | "FINE_CHARGED"
  | "PAYMENT_CARD_NOT_FOUND"
  | "PAYMENT_ALREADY_DONE"
  | "PAYMENT_CARD_NOT_EXISTS"
  | "PAYMENT_AMOUNT_NOT_SET"
  | "PAYMENT_BILL_STATUS_ERROR"
  | "INVALID_CARD_EXPIRATION"
  | "NO_NOTIFICATIONS"
  | "NOTIFICATION_MARKED_AS_SEEN"
  | "NOTIFICATIONS_COUNT"
  | "PATIENT_CONSENT_FORMS_SIGNED"
  | "PATIENT_CONSENT_FORM_NOT_FOUND"
  | "TEXTMACRO_SHORTCUT_EXISTS"
  | "TEXTMACRO_ADDED"
  | "TEXTMACRO_DATA_FETCHED"
  | "TEXTMACRO_UPDATED"
  | "FAILED_UPDATE_TEXT_MACRO"
  | "FAILED_TEXTMACRO_ARCHIVE"
  | "TEXTMACRO_ARCHIVE"
  | "TEXTMACRO_RESTORED"
  | "FAILED_TEXTMACRO_RESTORE"
  | "TEXTMACRO_NOT_EXIST"
  | "TASK_ADDED"
  | "TASK_DATA_FETCHED"
  | "TASK_UPDATED"
  | "TASK_ARCHIVED"
  | "TASK_NOT_EXIST"
  | "FAILED_UPDATE_TASK"
  | "INVALID_TASK_STATUS"
  | "FAILED_TASK_ARCHIVE"
  | "INVALID_DUEDATE"
  | "FINE_CHARGED_APPOINTMENT"
  | "APPOINTMENT_FINE"
  | "CANNOT_CHARGE_FINE"
  | "PAYMENT_SUCCESSFUL"
  | "VITAL_ADDED"
  | "VITAL_UPDATED"
  | "VITAL_DATA_FETCHED"
  | "VITAL_NOT_FOUND"
  | "FAILED_VITAL_UPDATED"
  | "EMITTED_SUCCESSFULLY"
  | "WEBHOOK_SUCCESSFUL"
  | "BILL_UPDATED"
  | "CLAIM_ADDED"
  | "BOOK_APPOINTMENT_ERROR"
  | "CANCEL_APPOINTMENT_ERROR"
  | "NOT_IMPLEMENTED"
  | "COMMENT_ADDED_SUCCESSFULLY"
  | "COMMENT_UPDATED_SUCCESSFULLY"
  | "COMMENT_DELETED_SUCCESSFULLY"
  | "MEDICAL_CODE_ADDED_SUCCESSFULLY"
  | "MEDICAL_CODE_UPDATED_SUCCESSFULLY"
  | "PROVIDER_REVIEW_ADDED_SUCCESSFULLY"
  | "PROVIDER_REVIEW_STATUS_UPDATED_SUCCESSFULLY"
  | "CHECK_IN_STATUS"
  | "REFILL_RX_ADDED"
  | "REFILL_RX_UPDATED"
  | "ROI_ADDED"
  | "ROI_UPDATED"
  | "ROI_STATUS_UPDATED"
  | "ROI_NOT_FOUND"
  | "INTERNAL_SERVER_ERROR"
  | "FILE_UPLOADED"
  | "EMPTY_FILE"
  | "MEDICAL_CODES_CREATED"
  | "MEDICAL_CODES_UPDATED"
  | "MEDICAL_CODE_ENABLED"
  | "MEDICAL_CODE_DISABLED"
  | "MEDICAL_CODE_ARCHIVED"
  | "MEDICAL_CODE_UNARCHIVED"
  | "PATIENT_ALLERGY_CREATED"
  | "PATIENT_ALLERGY_UPDATED"
  | "PATIENT_ALLERGY_ENABLED"
  | "PATIENT_ALLERGY_DISABLED"
  | "PATIENT_ALLERGY_ARCHIVED"
  | "PATIENT_ALLERGY_UNARCHIVED"
  | "PATIENT_VACCINE_CREATED"
  | "PATIENT_VACCINE_UPDATED"
  | "PATIENT_VACCINE_ENABLED"
  | "PATIENT_VACCINE_DISABLED"
  | "PATIENT_VACCINE_ARCHIVED"
  | "PATIENT_VACCINE_UNARCHIVED"
  | "PATIENT_VITAL_CREATED"
  | "PATIENT_VITAL_UPDATED"
  | "STICKY_NOTE_CREATED"
  | "STICKY_NOTE_UPDATED"
  | "STICKY_NOTE_ENABLED"
  | "STICKY_NOTE_DISABLED"
  | "STICKY_NOTE_ARCHIVED"
  | "STICKY_NOTE_UNARCHIVED"
  | "PATIENT_MEDICATION_CREATED"
  | "PATIENT_MEDICATION_UPDATED"
  | "PATIENT_MEDICATION_ENABLE_DISABLE_RESPONSE"
  | "PATIENT_MEDICATION_ARCHIVE_STATUS_UPDATED"
  | "PATIENT_MEDICATION_ARCHIVED"
  | "PATIENT_MEDICATION_UNARCHIVED"
  | "PATIENT_MEDICAL_HISTORY_CREATED"
  | "PATIENT_MEDICAL_HISTORY_UPDATED"
  | "PATIENT_MEDICAL_HISTORY_ENABLE_DISABLE_RESPONSE"
  | "PATIENT_MEDICAL_HISTORY_ARCHIVE_STATUS_UPDATED"
  | "PATIENT_SURGICAL_HISTORY_CREATED"
  | "PATIENT_SURGICAL_HISTORY_UPDATED"
  | "PATIENT_SURGICAL_HISTORY_ENABLE_DISABLE_RESPONSE"
  | "PATIENT_SURGICAL_HISTORY_ARCHIVE_STATUS_UPDATED"
  | "PATIENT_FAMILY_HISTORY_CREATED"
  | "PATIENT_FAMILY_HISTORY_UPDATED"
  | "PATIENT_FAMILY_HISTORY_ENABLE_DISABLE_RESPONSE"
  | "PATIENT_FAMILY_HISTORY_ARCHIVE_STATUS_UPDATED"
  | "PATIENT_SOCIAL_HISTORY_CREATED"
  | "PATIENT_SOCIAL_HISTORY_UPDATED"
  | "PATIENT_SOCIAL_HISTORY_ENABLE_DISABLE_RESPONSE"
  | "PATIENT_SOCIAL_HISTORY_ARCHIVE_STATUS_UPDATED"
  | "PATIENT_MEDICATION_CREATED_SUCCESSFULLY"
  | "PATIENT_MEDICATION_UPDATED_SUCCESSFULLY"
  | "DOCUMENT_TAG_CREATED"
  | "DOCUMENT_TAG_UPDATED"
  | "DOCUMENT_TAG_ENABLED"
  | "DOCUMENT_TAG_DISABLED"
  | "DOCUMENT_TAG_ARCHIVED"
  | "DOCUMENT_TAG_UNARCHIVED"
  | "PATIENT_LAB_ORDER_CREATED"
  | "PATIENT_LAB_ORDER_UPDATED"
  | "PATIENT_LAB_ORDER_NOT_FOUND"
  | "PATIENT_LAB_ORDER_ENABLED"
  | "PATIENT_LAB_ORDER_DISABLED"
  | "PATIENT_LAB_ORDER_ARCHIVED"
  | "PATIENT_LAB_ORDER_UNARCHIVED"
  | "CLINICAL_NOTE_ALREADY_FOUND"
  | "CLINICAL_NOTE_CREATED"
  | "CLINICAL_NOTE_UPDATED"
  | "CLINICAL_NOTE_NOT_FOUND"
  | "CLINICAL_NOTE_ENABLED"
  | "CLINICAL_NOTE_DISABLED"
  | "CLINICAL_NOTE_ARCHIVED"
  | "CLINICAL_NOTE_UNARCHIVED"
  | "USER_DOCUMENT_UPLOAD"
  | "ROLE_ADDED"
  | "PRIVILEGE_NOT_FOUND"
  | "ROLE_UPDATED"
  | "ROLE_SYNC"
  | "ROLE_NOT_FOUND"
  | "CUSTOM_FORM_CREATED"
  | "CUSTOM_FORM_STATUS_UPDATED"
  | "CUSTOM_FORM_UPDATED"
  | "CUSTOM_FORM_ENABLED"
  | "CUSTOM_FORM_DISABLED"
  | "CUSTOM_FORM_ARCHIVED"
  | "CUSTOM_FORM_RESTORED"
  | "CUSTOM_FORM_COPY"
  | "CUSTOM_FORM_TEMPLATE_CREATED"
  | "CUSTOM_FORM_TEMPLATE_STATUS_UPDATED"
  | "CUSTOM_FORM_TEMPLATE_UPDATED"
  | "CUSTOM_FORM_TEMPLATE_ENABLED"
  | "CUSTOM_FORM_TEMPLATE_DISABLED"
  | "CUSTOM_FORM_TEMPLATE_ARCHIVED"
  | "CUSTOM_FORM_TEMPLATE_RESTORED"
  | "MICROS_FORM_CREATED"
  | "MICROS_FORM_UPDATED"
  | "MICROS_FORM_STATUS_UPDATED"
  | "MICROS_FORM_ARCHIVE_STATUS_UPDATED"
  | "VISIT_NOTE_FORM_CREATED"
  | "VISIT_NOTE_FORM_UPDATED"
  | "VISIT_NOTE_FORM_STATUS_UPDATED"
  | "VISIT_NOTE_FORM_ARCHIVE_STATUS_UPDATED"
  | "REVIEW_OF_SYSTEM_FORM_CREATED"
  | "REVIEW_OF_SYSTEM_FORM_UPDATED"
  | "REVIEW_OF_SYSTEM_FORM_STATUS_UPDATED"
  | "REVIEW_OF_SYSTEM_FORM_ARCHIVE_STATUS_UPDATED"
  | "CUSTOM_QUESTIONNAIRE_FORM_CREATED"
  | "CUSTOM_QUESTIONNAIRE_FORM_UPDATED"
  | "CUSTOM_QUESTIONNAIRE_FORM_STATUS_UPDATED"
  | "CUSTOM_QUESTIONNAIRE_FORM_ARCHIVE_STATUS_UPDATED"
  | "CARE_PLAN_CREATED"
  | "CARE_PLAN_UPDATED"
  | "CARE_PLAN_ENABLED"
  | "CARE_PLAN_DISABLED"
  | "CARE_PLAN_ARCHIVE_STATUS_UPDATED"
  | "CARE_PLAN_ARCHIVED"
  | "CARE_PLAN_UNARCHIVED"
  | "CARE_PLAN_ASSIGNED"
  | "PATIENT_CARE_PLAN_STATUS_UPDATED"
  | "VITAL_REFERENCE_RANGE_UPDATED"
  | "PATIENT_VITAL_REFERENCE_RANGE_UPDATED"
  | "ANNOTABLE_IMAGE_FORM_CREATED"
  | "ANNOTABLE_IMAGE_FORM_UPDATED"
  | "ANNOTABLE_IMAGE_FORM_STATUS_UPDATED"
  | "ANNOTABLE_IMAGE_FORM_ARCHIVE_STATUS_UPDATED"
  | "ORDER_SET_FORM_CREATED"
  | "ORDER_SET_FORM_UPDATED"
  | "ORDER_SET_FORM_STATUS_UPDATED"
  | "PHYSICAL_EXAM_FORM_CREATED"
  | "PHYSICAL_EXAM_FORM_UPDATED"
  | "PHYSICAL_EXAM_FORM_STATUS_UPDATED"
  | "PHYSICAL_EXAM_FORM_ARCHIVE_STATUS_UPDATED"
  | "PATIENT_DIAGNOSIS_CREATED"
  | "PATIENT_DIAGNOSIS_UPDATED"
  | "PATIENT_DIAGNOSIS_ARCHIVED"
  | "PATIENT_DIAGNOSIS_UNARCHIVED"
  | "PATIENT_DIAGNOSIS_ARCHIVE_STATUS"
  | "PATIENT_VITAL_CREATED_SUCCESSFULLY"
  | "PATIENT_VITAL_UPDATED_SUCCESSFULLY"
  | "PATIENT_VITAL_SETTING_UPDATED"
  | "CHECK_CONSULT_TIME"
  | "EVENT_EMITTED_SUCCESSFULLY"
  | "NOTIFICATION_SEND"
  | "TIME_LOG_CREATED"
  | "TIME_LOG_UPDATED"
  | "TIME_LOG_DELETED"
  | "PATIENT_TRAINED"
  | "AUTOMATIC_TIME_LOG_CREATED"
  | "NURSE_ACTIONS_STATISTIC_GENERATED"
  | "AUDIO_QUICK_SUMMARY_GENERATED"
  | "INVITATION_SEND_SUCCESSFULLY"
  | "EMAIL_SENT"
  | "SMS_SENT"
  | "GATEWAY_TIMEOUT"
  | "NURSE_REASSIGNED_SUCCESSFULLY"
  | "UPDATE_PROVIDER_GROUP_CONFIGURATION";

export type Address = {
  line1: string;
  line2?: string;
  city: string;
  state: string;
  country: string;
  zipcode: string;
};

export type User = {
  uuid?: string;
  iamId?: string;
  email?: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  phone: string;
  gender: "MALE" | "FEMALE" | "OTHER";
  readonly avatar?: string;
  birthDate?: string;
  roleType?: "PROVIDER" | "STAFF" | "PATIENT";
  role:
    | "SUPER_ADMIN"
    | "ADMIN"
    | "FRONTDESK"
    | "BILLER"
    | "SITE_ADMIN"
    | "PROVIDER_GROUP_ADMIN"
    | "PROVIDER"
    | "NURSE"
    | "PATIENT"
    | "ANONYMOUS";
  address?: Address;
  lastLogin?: string;
  active?: boolean;
  readonly archive?: boolean;
  readonly emailVerified?: boolean;
  readonly phoneVerified?: boolean;
  password?: string;
  tenantKey?: string;
  acceptTerms?: boolean;
  locationId?: string;
  locationName?: string;
  currentMonthSummary?: boolean;
  selfCheckUuid?: string;
};

export type gender = "MALE" | "FEMALE" | "OTHER";

export type roleType = "PROVIDER" | "STAFF" | "PATIENT";

export type role =
  | "SUPER_ADMIN"
  | "ADMIN"
  | "FRONTDESK"
  | "BILLER"
  | "SITE_ADMIN"
  | "PROVIDER_GROUP_ADMIN"
  | "PROVIDER"
  | "NURSE"
  | "PATIENT"
  | "ANONYMOUS";

export type TimeLogRequest = {
  uuid?: string;
  patientId?: string;
  logIdentifier?: string;
  logTimeDuration?: number;
  logStartTime?: string;
  logEndTime?: string;
  activityName?: string;
  logEntryType?: "AUTOMATIC" | "MANUAL";
  note?: string;
  billingCycle?: string;
  loggedBy?: string;
  loggedByExternal?: boolean;
  totalTimeLog?: number;
};

export type logEntryType = "AUTOMATIC" | "MANUAL";

export type Appointment = {
  uuid?: string;
  purpose: string;
  patientId?: string;
  nurseId: string;
  external: boolean;
  startTime: string;
  endTime: string;
  requestedStartTime?: string;
  requestedEndTime?: string;
  requestedDuration?: number;
  requestedTimezone?: string;
  duration: number;
  timezone:
    | "PST"
    | "EST"
    | "CST"
    | "MST"
    | "AST"
    | "HST"
    | "EDT"
    | "PDT"
    | "CDT"
    | "ADT"
    | "MDT"
    | "IST"
    | "SGT"
    | "AKDT"
    | "AKST"
    | "UTC"
    | "WIT";
  patientName?: string;
  patientMrn?: string;
  patientEmail?: string;
  patientPhone?: string;
  providerName?: string;
  nurseName?: string;
  mode?: "HOME_VISIT" | "TELE_VISIT";
  status?:
    | "PENDING"
    | "ACCEPTED"
    | "REJECTED"
    | "CONFIRMED"
    | "REQUESTED"
    | "CANCELLED"
    | "NO_SHOW"
    | "CHECKED_IN"
    | "IN_PROGRESS"
    | "COMPLETED"
    | "SCHEDULED"
    | "RESCHEDULED"
    | "BROADCAST"
    | "REVOKE"
    | "IN_EXAM";
  rescheduleReason?: string;
  cancelReason?: string;
  slotOpen?: boolean;
  created?: string;
  archive?: boolean;
  reason?: string;
  address?: Address;
  broadcastNurseName?: string;
  broadcastNurseAvatar?: string;
  patientAvatar?: string;
  nurseAvatar?: string;
  broadcast?: boolean;
  broadcastBy?: string;
  requesterType?: "PATIENT" | "STAFF";
  noshow?: "PATIENT" | "NURSE";
  clinicalNote?: ClinicalNote;
  escalated?: boolean;
  prevStatus?:
    | "PENDING"
    | "ACCEPTED"
    | "REJECTED"
    | "CONFIRMED"
    | "REQUESTED"
    | "CANCELLED"
    | "NO_SHOW"
    | "CHECKED_IN"
    | "IN_PROGRESS"
    | "COMPLETED"
    | "SCHEDULED"
    | "RESCHEDULED"
    | "BROADCAST"
    | "REVOKE"
    | "IN_EXAM";
  cptCode?: string[];
  sourceTaskId?: string;
};

export type timezone =
  | "PST"
  | "EST"
  | "CST"
  | "MST"
  | "AST"
  | "HST"
  | "EDT"
  | "PDT"
  | "CDT"
  | "ADT"
  | "MDT"
  | "IST"
  | "SGT"
  | "AKDT"
  | "AKST"
  | "UTC"
  | "WIT";

export type mode = "HOME_VISIT" | "TELE_VISIT";

export type status =
  | "PENDING"
  | "ACCEPTED"
  | "REJECTED"
  | "CONFIRMED"
  | "REQUESTED"
  | "CANCELLED"
  | "NO_SHOW"
  | "CHECKED_IN"
  | "IN_PROGRESS"
  | "COMPLETED"
  | "SCHEDULED"
  | "RESCHEDULED"
  | "BROADCAST"
  | "REVOKE"
  | "IN_EXAM";

export type requesterType = "PATIENT" | "STAFF";

export type noshow = "PATIENT" | "NURSE";

export type prevStatus =
  | "PENDING"
  | "ACCEPTED"
  | "REJECTED"
  | "CONFIRMED"
  | "REQUESTED"
  | "CANCELLED"
  | "NO_SHOW"
  | "CHECKED_IN"
  | "IN_PROGRESS"
  | "COMPLETED"
  | "SCHEDULED"
  | "RESCHEDULED"
  | "BROADCAST"
  | "REVOKE"
  | "IN_EXAM";

export type ClinicalNote = {
  uuid?: string;
  note: string;
  status?: "SIGNED_OFF" | "IN_PROGRESS" | "PENDING" | "COMPLETED";
  appointmentId?: string;
};

export type status2 = "SIGNED_OFF" | "IN_PROGRESS" | "PENDING" | "COMPLETED";

export type MedicalCodesEntity = {
  createdBy?: string;
  modifiedBy?: string;
  created?: string;
  modified?: string;
  id?: number;
  uuid?: string;
  type?: "ICD10" | "CPT" | "ALL";
  code?: string;
  description?: string;
  active?: boolean;
  archive?: boolean;
};

export type type = "ICD10" | "CPT" | "ALL";

export type Note = {
  uuid?: string;
  name?: string;
  goalProgress?: "NOT_STARTED" | "PROGRESSING" | "COMPLETED" | "ON_HOLD" | "CANCELLED" | "RESOLVED";
};

export type goalProgress = "NOT_STARTED" | "PROGRESSING" | "COMPLETED" | "ON_HOLD" | "CANCELLED" | "RESOLVED";

export type PatientVital = {
  uuid?: string;
  patientId: string;
  vitalName: string;
  value1: number;
  value2?: number;
  ecgValue?: string;
  recordedDate: string;
  unit?: string;
  integrationId?: string;
  heartRate?: number;
  integrationType?: "DEVICE" | "EHR" | "MANUAL" | "WATCH" | "IHEALTH";
  note?: Note;
  created?: string;
  modified?: string;
  minRange?: string;
  maxRange?: string;
  severity?: string;
  alertStatus?: "RESOLVED" | "NOT_RESOLVED";
};

export type integrationType = "DEVICE" | "EHR" | "MANUAL" | "WATCH" | "IHEALTH";

export type alertStatus = "RESOLVED" | "NOT_RESOLVED";

export type Task = {
  uuid?: string;
  title: string;
  patientUuid?: string;
  patientName?: string;
  birthDate?: string;
  gender?: "MALE" | "FEMALE" | "OTHER";
  mrn?: string;
  diagnosisCodes?: MedicalCodesEntity[];
  assignTo?: string;
  assignedExternal?: boolean;
  assignedName?: string;
  assignBy?: string;
  assignedByName?: string;
  assignedByExternal?: boolean;
  dueDate: string;
  status: "PENDING" | "COMPLETED" | "DISCARDED";
  priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  roleType?: "STAFF" | "PROVIDER" | "SELF";
  role?:
    | "SUPER_ADMIN"
    | "ADMIN"
    | "FRONTDESK"
    | "BILLER"
    | "SITE_ADMIN"
    | "PROVIDER_GROUP_ADMIN"
    | "PROVIDER"
    | "NURSE"
    | "PATIENT"
    | "ANONYMOUS";
  archive?: boolean;
  note?: string;
  completionDate?: string;
  sourceReferenceId?: string;
  type?: string;
  patientVital?: PatientVital;
  appointment?: Appointment;
  assignedDate?: string;
  description?: string;
};

export type status3 = "PENDING" | "COMPLETED" | "DISCARDED";

export type priority = "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";

export type roleType2 = "STAFF" | "PROVIDER" | "SELF";

export type Privilege = {
  id: number;
  name?: string;
  description?: string;
  module?: string;
  granted?: boolean;
};

export type Role = {
  id?: number;
  name: string;
  type: string;
  privileges?: Privilege[];
};

export type RolePrivilegeUpdateRequest = {
  roleId: number;
  privilegeId: number;
  granted: boolean;
};

export type LicenseState = {
  id?: number;
  country?: string;
  state?: string;
};

export type Provider = {
  uuid?: string;
  providerType?: "MD" | "PA" | "PSYD" | "LCSW" | "NP" | "RN" | "BHNP" | "FNP" | "RD" | "NONE" | "UNKNOWN" | "NPS";
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  videoLink?: string;
  avatar?: string;
  role:
    | "SUPER_ADMIN"
    | "ADMIN"
    | "FRONTDESK"
    | "BILLER"
    | "SITE_ADMIN"
    | "PROVIDER_GROUP_ADMIN"
    | "PROVIDER"
    | "NURSE"
    | "PATIENT"
    | "ANONYMOUS";
  active?: boolean;
  archive?: boolean;
  gender: "MALE" | "FEMALE" | "OTHER";
  npi: string;
  introduction?: string;
  chatbotTone?: "PROFESSIONAL" | "FRIENDLY" | "CASUAL";
  onboardStatus?: "PENDING" | "COMPLETED";
  providerLicenseDetails?: ProviderLicenseDetails[];
  emailVerified?: boolean;
  acceptTerms?: boolean;
  address: Address;
  schemaType?: "INTERNAL" | "EXTERNAL";
  ehrId?: string;
  ehrName?: string;
  userId?: string;
  selfCheckUuid?: string;
};

export type providerType =
  | "MD"
  | "PA"
  | "PSYD"
  | "LCSW"
  | "NP"
  | "RN"
  | "BHNP"
  | "FNP"
  | "RD"
  | "NONE"
  | "UNKNOWN"
  | "NPS";

export type chatbotTone = "PROFESSIONAL" | "FRIENDLY" | "CASUAL";

export type onboardStatus = "PENDING" | "COMPLETED";

export type schemaType = "INTERNAL" | "EXTERNAL";

export type ProviderLicenseDetails = {
  uuid?: string;
  licenseNumber?: string;
  expiryDate?: string;
  licensedStates?: LicenseState[];
};

export type ChangeAvatarRequest = {
  newAvatar?: string;
};

export type ProviderGroup = {
  uuid?: string;
  name: string;
  schema?: string;
  subdomain: string;
  phone: string;
  npi: string;
  email: string;
  timezone?: string;
  address: Address;
  active?: boolean;
  archive?: boolean;
  avatar?: string;
  ehrId?: string;
  ehrName?: string;
  notificationEmail?: string;
  billingCycle?: "NO_BILLING" | "ROLLING_30_DAYS" | "CALENDAR_MONTH";
  monitoringThreshold?: number;
};

export type billingCycle = "NO_BILLING" | "ROLLING_30_DAYS" | "CALENDAR_MONTH";

export type EmergencyContact = {
  uuid?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
};

export type Patient = {
  uuid?: string;
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber?: string;
  gender?: "MALE" | "FEMALE" | "OTHER";
  middleName?: string;
  mrn?: string;
  birthDate?: string;
  avatar?: string;
  providerId: {
    [key: string]: string;
  };
  nurseId: {
    [key: string]: string;
  };
  schemaType: "INTERNAL" | "EXTERNAL";
  address?: Address;
  signature?: string;
  signatureChanged?: boolean;
  emergencyContact?: EmergencyContact;
  consentFormSigned?: boolean;
  acceptTerms?: boolean;
  source?: string;
  providerNpi?: string;
  nurseEmail?: string;
  nurseAvatar?: string;
  emailVerified?: boolean;
  active?: boolean;
  archive?: boolean;
  role?:
    | "SUPER_ADMIN"
    | "ADMIN"
    | "FRONTDESK"
    | "BILLER"
    | "SITE_ADMIN"
    | "PROVIDER_GROUP_ADMIN"
    | "PROVIDER"
    | "NURSE"
    | "PATIENT"
    | "ANONYMOUS";
  ehrId?: string;
  ehrName?: string;
  diagnosisCodes?: string[];
  patientCarePlanRequest?: PatientCarePlanRequest;
  carePlanAssigned?: boolean;
  height?: string;
  selfCheckUuid?: string;
};

export type PatientCarePlanRequest = {
  uuid?: string;
  carePlanId: string;
  startDate?: string;
  vitalReferences: VitalReference[];
  protocolType?: "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";
  globalCarePlan: boolean;
};

export type protocolType = "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";

export type VitalRange = {
  rangeType?:
    | "NORMAL"
    | "MODERATE"
    | "CRITICAL"
    | "LOW_MODERATE"
    | "HIGH_MODERATE"
    | "NORMAL_SYSTOLIC"
    | "NORMAL_DIASTOLIC"
    | "LOW_MODERATE_SYSTOLIC"
    | "LOW_MODERATE_DIASTOLIC"
    | "HIGH_MODERATE_SYSTOLIC"
    | "HIGH_MODERATE_DIASTOLIC"
    | "CRITICAL_SYSTOLIC"
    | "CRITICAL_DIASTOLIC";
  min?: number;
  max?: number;
};

export type rangeType =
  | "NORMAL"
  | "MODERATE"
  | "CRITICAL"
  | "LOW_MODERATE"
  | "HIGH_MODERATE"
  | "NORMAL_SYSTOLIC"
  | "NORMAL_DIASTOLIC"
  | "LOW_MODERATE_SYSTOLIC"
  | "LOW_MODERATE_DIASTOLIC"
  | "HIGH_MODERATE_SYSTOLIC"
  | "HIGH_MODERATE_DIASTOLIC"
  | "CRITICAL_SYSTOLIC"
  | "CRITICAL_DIASTOLIC";

export type VitalReference = {
  uuid?: string;
  carePlanId?: string;
  vitalType: string;
  targetRange?: string;
  vitalRanges?: VitalRange[];
  deviceName?: string;
};

export type PatientMedication = {
  id?: number;
  uuid?: string;
  patientId: string;
  medicineName: string;
  startDate?: string;
  endDate?: string;
  quantity?: string;
  note?: string;
  direction?: string;
  medicationDataSource?: "MANUAL" | "EHR" | "AI";
  timezone?: string;
  medicineImage?: string;
  medicineAudio?: string;
  medicineType?: "TABLET" | "SYRUP";
  handwritten?: boolean;
  active?: boolean;
  archive?: boolean;
  medicationEhrId?: string;
  effiectivePeriodStartDate?: string;
  effiectivePeriodEndDate?: string;
  patientMedicationDosage?: PatientMedicationDosage[];
  patientMedicationDosageEntities?: PatientMedicationDosageEntity[];
};

export type medicationDataSource = "MANUAL" | "EHR" | "AI";

export type medicineType = "TABLET" | "SYRUP";

export type PatientMedicationDosage = {
  id?: number;
  patientInstruction?: string;
  route?: string;
  timing?: string;
  frequency?: string;
  unit?: string;
  dosage?: number;
};

export type PatientMedicationDosageEntity = {
  id?: number;
  patientInstruction?: string;
  route?: string;
  timing?: string;
  frequency?: string;
  unit?: string;
  dosage?: number;
};

export type DiagnosisEvidence = {
  name?: string;
};

export type PatientDiagnosis = {
  uuid?: string;
  patientId: string;
  name?: string;
  medicalCode?: string;
  type?: "CHRONIC" | "ACUTE" | "PRIMARY" | "SECONDARY" | "DIFFERENTIAL" | "PROVISIONAL" | "RESOLVED";
  startDate?: string;
  onSetDate?: string;
  recordedDate?: string;
  encounterEhrId?: string;
  note?: string;
  active?: boolean;
  archive?: boolean;
  evidence?: DiagnosisEvidence[];
  lastOccurrence?: string;
  modified?: string;
  modifiedBy?: string;
  diagnosisEhrId?: string;
  medicalCodeType?: string;
  integrationType?: "DEVICE" | "EHR" | "MANUAL" | "WATCH" | "IHEALTH";
};

export type type2 = "CHRONIC" | "ACUTE" | "PRIMARY" | "SECONDARY" | "DIFFERENTIAL" | "PROVISIONAL" | "RESOLVED";

export type PatientCarePlanUpdateRequest = {
  uuid?: string;
  vitalReference: VitalReference[];
  carePlanStatus?: "IN_PROGRESS" | "PENDING" | "COMPLETED";
};

export type carePlanStatus = "IN_PROGRESS" | "PENDING" | "COMPLETED";

export type PatientCarePlanStatusChange = {
  uuid?: string;
  carePlanStatus?: "IN_PROGRESS" | "PENDING" | "COMPLETED";
  bmi?: string;
  bloodPressure?: string;
};

export type PatientProgramGoalTrack = {
  uuid?: string;
  programGoalId?: string;
  trackDate?: string;
  tracked?: boolean;
};

export type BulkCarePlanRequest = {
  carePlanId: string;
  startDate: string;
  patientIds: string[];
  globalCarePlan: boolean;
};

export type PatientAllergy = {
  uuid?: string;
  patientId: string;
  allergyType: "DRUG" | "FOOD" | "ENVIRONMENT" | "OTHER";
  name: string;
  reaction?:
    | "PAIN"
    | "RUNNY_NOSE"
    | "SWELLING"
    | "BLOATING"
    | "VOMITING"
    | "RASHES"
    | "ITCHY_NOSE"
    | "THROAT_CLOSING"
    | "COUGH"
    | "REDNESS"
    | "UNKNOWN"
    | "HIVES";
  severity?: "MILD" | "HIGH" | "MODERATE" | "UNDEFINED";
  imported?: boolean;
  onSetDate?: string;
  recordedDate?: string;
  active?: boolean;
  archive?: boolean;
  allergyEhrId?: string;
  note?: string;
};

export type allergyType = "DRUG" | "FOOD" | "ENVIRONMENT" | "OTHER";

export type reaction =
  | "PAIN"
  | "RUNNY_NOSE"
  | "SWELLING"
  | "BLOATING"
  | "VOMITING"
  | "RASHES"
  | "ITCHY_NOSE"
  | "THROAT_CLOSING"
  | "COUGH"
  | "REDNESS"
  | "UNKNOWN"
  | "HIVES";

export type severity = "MILD" | "HIGH" | "MODERATE" | "UNDEFINED";

export type MedicalCode = {
  uuid?: string;
  type: "ICD10" | "CPT" | "ALL";
  code: string;
  description?: string;
  active?: boolean;
  archive?: boolean;
  errorMessage?: string;
};

export type Location = {
  uuid?: string;
  name: string;
  locationId?: string;
  phone: string;
  timezone?: string;
  email: string;
  address: Address;
  locationHours?: LocationHour[];
  active?: boolean;
  archive?: boolean;
  ehrId?: string;
  ehrName?: string;
};

export type LocationHour = {
  dayOfWeek?: "MONDAY" | "TUESDAY" | "WEDNESDAY" | "THURSDAY" | "FRIDAY" | "SATURDAY" | "SUNDAY";
  openingTime?: string;
  closingTime?: string;
};

export type dayOfWeek = "MONDAY" | "TUESDAY" | "WEDNESDAY" | "THURSDAY" | "FRIDAY" | "SATURDAY" | "SUNDAY";

export type Device = {
  uuid?: string;
  name: string;
  deviceType?: string;
  description?: string;
  guideLink?: string;
  active?: boolean;
  archive?: boolean;
  category?: "MECHANICAL" | "ELECTRICAL" | "DIGITAL";
  createdBy?: string;
  created?: string;
  modified?: string;
};

export type category = "MECHANICAL" | "ELECTRICAL" | "DIGITAL";

export type ConsentFormTemplate = {
  uuid?: string;
  name: string;
  document: string;
  active?: boolean;
  archive?: boolean;
  changeConsent?: boolean;
  signed?: boolean;
};

export type CarePlan = {
  uuid?: string;
  title?: string;
  duration?: number;
  durationUnit?: "DAY" | "WEEK" | "MONTH" | "YEAR";
  overview?: string;
  gender?: "MALE" | "FEMALE" | "UNISEX";
  ageCriteria?: string;
  age?: string;
  deviceName?: string[];
  devices?: Device[];
  routineCheckup?: string;
  programGoals?: ProgramGoal[];
  vitalReferences?: VitalReference[];
  globalCarePlan?: boolean;
  external?: boolean;
  protocolType?: "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";
  active?: boolean;
  archive?: boolean;
  trackedVitals?: string[];
  modified?: string;
  diagnosisCodes: string[];
  protocol?: Protocol[];
};

export type durationUnit = "DAY" | "WEEK" | "MONTH" | "YEAR";

export type gender2 = "MALE" | "FEMALE" | "UNISEX";

export type ProgramGoal = {
  category?: string;
  title?: string;
  uuid?: string;
  trackBy?: "DAY" | "WEEK" | "MONTH" | "YEAR";
  targetType?: string;
  targetValue?: string;
  unit?: string;
  active?: boolean;
  objective?: string;
  programGoalTasks?: ProgramGoalTask[];
  goalTracks?: PatientProgramGoalTrack[];
  percentage?: string;
  bmi?: string;
  bloodPressure?: string;
  checkedDaysCount?: number;
  skippedDaysCount?: number;
};

export type trackBy = "DAY" | "WEEK" | "MONTH" | "YEAR";

export type ProgramGoalTask = {
  task?: string;
  details?: string;
};

export type Protocol = {
  uuid?: string;
  title?: string;
  protocolType?: "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";
  range?: string;
  description?: string[];
};

export type AppointmentStatusChange = {
  uuid: string;
  status?:
    | "PENDING"
    | "ACCEPTED"
    | "REJECTED"
    | "CONFIRMED"
    | "REQUESTED"
    | "CANCELLED"
    | "NO_SHOW"
    | "CHECKED_IN"
    | "IN_PROGRESS"
    | "COMPLETED"
    | "SCHEDULED"
    | "RESCHEDULED"
    | "BROADCAST"
    | "REVOKE"
    | "IN_EXAM";
  nurseId?: string;
  schemaType?: "INTERNAL" | "EXTERNAL";
  reason?: string;
  noshow?: "PATIENT" | "NURSE";
};

export type RescheduleRequest = {
  appointmentId: string;
  duration: number;
  startTime: string;
  endTime: string;
  reason: string;
  timezone:
    | "PST"
    | "EST"
    | "CST"
    | "MST"
    | "AST"
    | "HST"
    | "EDT"
    | "PDT"
    | "CDT"
    | "ADT"
    | "MDT"
    | "IST"
    | "SGT"
    | "AKDT"
    | "AKST"
    | "UTC"
    | "WIT";
  nurseId?: string;
  external?: boolean;
};

export type PlayerTimeLogRequestDto = {
  timeLogRequest?: TimeLogRequest[];
};

export type ResetPasswordRequest = {
  newPassword: string;
  email: string;
  otp: string;
};

export type AvailabilitySetting = {
  providerId: string;
  bookingWindow: number;
  timezone:
    | "PST"
    | "EST"
    | "CST"
    | "MST"
    | "AST"
    | "HST"
    | "EDT"
    | "PDT"
    | "CDT"
    | "ADT"
    | "MDT"
    | "IST"
    | "SGT"
    | "AKDT"
    | "AKST"
    | "UTC"
    | "WIT";
  initialConsultTime: number;
  followupConsultTime: number;
  bufferTime?: number;
  bookBefore?: string;
  blockDays?: BlockDay[];
  daySlots?: DaySlot[];
};

export type BlockDay = {
  startTime?: string;
  endTime?: string;
};

export type DaySlot = {
  day?: "MONDAY" | "TUESDAY" | "WEDNESDAY" | "THURSDAY" | "FRIDAY" | "SATURDAY" | "SUNDAY";
  startTime?: LocalTime;
  endTime?: LocalTime;
};

export type day = "MONDAY" | "TUESDAY" | "WEDNESDAY" | "THURSDAY" | "FRIDAY" | "SATURDAY" | "SUNDAY";

export type LocalTime = {
  hour?: number;
  minute?: number;
  second?: number;
  nano?: number;
};

export type PatientVitalRequest = {
  patientId: string;
  patientVital?: PatientVital[];
};

export type PatientDeviceRequest = {
  deviceModelId?: string;
  patientId?: string;
  trainingConfirmed?: boolean;
};

export type PatientConsentForm = {
  uuid?: string;
  patientUuid: string;
  patientName?: string;
  signature?: string;
  consentFormTemplate: ConsentFormTemplate;
  modified?: string;
};

export type SendDirectMessageRequest = {
  subject?: string;
  content?: string;
  type: "EMAIL" | "SMS";
  email?: string;
  phone?: string;
  attachmentName?: string;
};

export type type3 = "EMAIL" | "SMS";

export type MessageRequest = {
  registrationTokens?: string[];
  title?: string;
  body?: string;
  response?: string;
  notificationType?:
    | "PATIENT_APPOINTMENT_ACCEPTED"
    | "PATIENT_APPOINTMENT_REQUEST"
    | "BROADCAST"
    | "APPOINTMENT_REMINDER"
    | "PATIENT_MEDICATION_REMINDER"
    | "CARE_PLAN_REMINDER"
    | "PATIENT_VITAL_ALERT"
    | "SMART_ALERT_REMINDER";
};

export type notificationType =
  | "PATIENT_APPOINTMENT_ACCEPTED"
  | "PATIENT_APPOINTMENT_REQUEST"
  | "BROADCAST"
  | "APPOINTMENT_REMINDER"
  | "PATIENT_MEDICATION_REMINDER"
  | "CARE_PLAN_REMINDER"
  | "PATIENT_VITAL_ALERT"
  | "SMART_ALERT_REMINDER";

export type LogoutRequest = {
  refreshToken: string;
};

export type LoginRequest = {
  username: string;
  password: string;
};

export type ChangePasswordRequest = {
  oldPassword: string;
  newPassword: string;
};

export type AppointmentRequest = {
  uuid?: string;
  patientId: string;
  reason?: string;
  status?:
    | "PENDING"
    | "ACCEPTED"
    | "REJECTED"
    | "CONFIRMED"
    | "REQUESTED"
    | "CANCELLED"
    | "NO_SHOW"
    | "CHECKED_IN"
    | "IN_PROGRESS"
    | "COMPLETED"
    | "SCHEDULED"
    | "RESCHEDULED"
    | "BROADCAST"
    | "REVOKE"
    | "IN_EXAM";
  mode: "HOME_VISIT" | "TELE_VISIT";
  purpose?: string;
  nurseId?: string;
  nurseName?: string;
  external?: boolean;
  startTime?: string;
  endTime?: string;
  requestedTime?: string;
  timezone?:
    | "PST"
    | "EST"
    | "CST"
    | "MST"
    | "AST"
    | "HST"
    | "EDT"
    | "PDT"
    | "CDT"
    | "ADT"
    | "MDT"
    | "IST"
    | "SGT"
    | "AKDT"
    | "AKST"
    | "UTC"
    | "WIT";
  cancelReason?: string;
  duration?: number;
  slotOpen?: boolean;
  archive?: boolean;
  cptCode?: string[];
};

export type UserInvitationRequest = {
  appointmentId: string;
  userIds?: string[];
  schemaType: "INTERNAL" | "EXTERNAL";
  invitationLink?: string;
  isSms?: boolean;
  isEmail?: boolean;
};

export type GenerateAvatarRequest = {
  gender?: string;
  nurseId?: string;
  schema?: string;
  image?: string;
  voiceMode?: string;
  voiceName?: string;
  reset?: boolean;
};

export type JsonNode = {
  [key: string]: unknown;
};

export type MedicationAudioRequest = {
  patientUuid?: string;
  schema?: string;
  results?: JsonNode;
};

export type Message = {
  role?: string;
  content?: string;
  timezone?: string;
};

export type PatientRequest = {
  patient_id?: number;
  schema?: string;
  age?: number;
  gender?: "MALE" | "FEMALE" | "OTHER";
  primary_diagnosis?: string;
  database?: string;
};

export type PatientDTO = {
  requests?: PatientRequest[];
};

export type SseEmitter = {
  timeout?: number;
};

export type EhrAccessToken = {
  access_token?: string;
  token_type?: string;
  expires_in?: number;
  scope?: string;
};

export type UpdateUserArchiveStatusData = {
  status: boolean;
  userId: string;
  xTenantId?: string;
};

export type UpdateUserArchiveStatusResponse = Response;

export type GetAllUsersData = {
  archive?: boolean;
  locationId?: string;
  page?: number;
  role?: string;
  roleType?: "PROVIDER" | "STAFF" | "PATIENT";
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  status?: boolean;
  xTenantId?: string;
};

export type GetAllUsersResponse = Response;

export type UpdateUserData = {
  requestBody: User;
  xTenantId?: string;
};

export type UpdateUserResponse = Response;

export type AddUserData = {
  requestBody: User;
  xTenantId?: string;
};

export type AddUserResponse = Response;

export type ChangeAvatar3Data = {
  requestBody: ChangeAvatarRequest;
  userUuid: string;
  xTenantId?: string;
};

export type ChangeAvatar3Response = Response;

export type VerifyUserData = {
  email: string;
  xTenantId?: string;
};

export type VerifyUserResponse = Response;

export type VerifyOtpData = {
  email: string;
  linkType: string;
  otp: string;
  xTenantId?: string;
};

export type VerifyOtpResponse = Response;

export type SetPasswordData = {
  linkType: string;
  requestBody: ResetPasswordRequest;
  xTenantId?: string;
};

export type SetPasswordResponse = Response;

export type ResendOtpData = {
  email: string;
  linkType: string;
  xTenantId?: string;
};

export type ResendOtpResponse = Response;

export type LogoutData = {
  requestBody: LogoutRequest;
  xTenantId?: string;
};

export type LogoutResponse = Response;

export type GetAccessTokenData = {
  requestBody: LoginRequest;
  xTenantId?: string;
};

export type GetAccessTokenResponse = Response;

export type ChangePasswordData = {
  requestBody: ChangePasswordRequest;
  xTenantId?: string;
};

export type ChangePasswordResponse = Response;

export type GetAccessTokenFromRefreshTokenData = {
  refreshToken: string;
  xTenantId?: string;
};

export type GetAccessTokenFromRefreshTokenResponse = Response;

export type GetUserData = {
  userId: string;
  xTenantId?: string;
};

export type GetUserResponse = Response;

export type GetProfile1Data = {
  xTenantId?: string;
};

export type GetProfile1Response = Response;

export type UpdatePatientVitalSettingData = {
  patientId: string;
  settingName: "HEART_RATE" | "ECG" | "HRV" | "OXYGEN_SATURATION" | "STRESS";
  status: boolean;
  xTenantId?: string;
};

export type UpdatePatientVitalSettingResponse = Response;

export type GetVitalSettingsData = {
  xTenantId?: string;
};

export type GetVitalSettingsResponse = Response;

export type GetPatientVitalSettingData = {
  patientUuid: string;
  xTenantId?: string;
};

export type GetPatientVitalSettingResponse = Response;

export type GetAllPatientTimeLogsData = {
  activityName?: string;
  loggedBy?: string;
  loggedEntryType?: string;
  month: string;
  page?: number;
  patientId: string;
  size?: number;
  sort?: string;
  sortBy?: string;
  xTenantId?: string;
};

export type GetAllPatientTimeLogsResponse = Response;

export type UpdateTimeLogData = {
  requestBody: TimeLogRequest;
  xTenantId?: string;
};

export type UpdateTimeLogResponse = Response;

export type CreateTimeLogAsyncData = {
  requestBody: TimeLogRequest;
  xTenantId?: string;
};

export type CreateTimeLogAsyncResponse = Response;

export type PlayerTimeLogData = {
  requestBody: PlayerTimeLogRequestDto;
  xTenantId?: string;
};

export type PlayerTimeLogResponse = Response;

export type GetTimeLogByIdData = {
  timeLogId: string;
  xTenantId?: string;
};

export type GetTimeLogByIdResponse = Response;

export type DeleteTimeLogByIdData = {
  timeLogId: string;
  xTenantId?: string;
};

export type DeleteTimeLogByIdResponse = Response;

export type GetAllTasksData = {
  active?: boolean;
  archive?: boolean;
  assignedBy?: string;
  assignedDate?: string;
  assignedTo?: string;
  currentUserUuid?: string;
  dueDate?: string;
  page?: number;
  patientId?: string;
  priority?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  searchAssignTo?: string;
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  status?: "PENDING" | "COMPLETED" | "DISCARDED";
  type?: string;
  xTenantId?: string;
};

export type GetAllTasksResponse = Response;

export type UpdateTaskData = {
  requestBody: Task;
  xTenantId?: string;
};

export type UpdateTaskResponse = Response;

export type AddTaskData = {
  requestBody: Task;
  xTenantId?: string;
};

export type AddTaskResponse = Response;

export type UpdateTaskStatusData = {
  note?: string;
  status: "PENDING" | "COMPLETED" | "DISCARDED";
  taskUuid: string;
  xTenantId?: string;
};

export type UpdateTaskStatusResponse = Response;

export type GetTaskByUuidData = {
  taskUuid: string;
  xTenantId?: string;
};

export type GetTaskByUuidResponse = Response;

export type ArchiveTaskData = {
  status: boolean;
  taskUuid: string;
  xTenantId?: string;
};

export type ArchiveTaskResponse = Response;

export type GetAllRolesData = {
  xTenantId?: string;
};

export type GetAllRolesResponse = Response;

export type UpdateRoleData = {
  requestBody: Role;
  xTenantId?: string;
};

export type UpdateRoleResponse = Response;

export type AddRoleData = {
  requestBody: Role;
  xTenantId?: string;
};

export type AddRoleResponse = Response;

export type AddUpdateRolePermissionsData = {
  xTenantId?: string;
};

export type AddUpdateRolePermissionsResponse = Response;

export type GetAllPrivilegesData = {
  xTenantId?: string;
};

export type GetAllPrivilegesResponse = Response;

export type UpdateRolePermissionData = {
  requestBody: RolePrivilegeUpdateRequest[];
  xTenantId?: string;
};

export type UpdateRolePermissionResponse = Response;

export type ResetAllRolePrivilegesData = {
  xTenantId?: string;
};

export type ResetAllRolePrivilegesResponse = Response;

export type GetAllRolesPermissionsData = {
  realm: string;
  xTenantId?: string;
};

export type GetAllRolesPermissionsResponse = Response;

export type GetAllProvidersData = {
  archive?: boolean;
  page?: number;
  role?:
    | "SUPER_ADMIN"
    | "ADMIN"
    | "FRONTDESK"
    | "BILLER"
    | "SITE_ADMIN"
    | "PROVIDER_GROUP_ADMIN"
    | "PROVIDER"
    | "NURSE"
    | "PATIENT"
    | "ANONYMOUS";
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  state?: string;
  status?: boolean;
  xTenantId?: string;
};

export type GetAllProvidersResponse = Response;

export type UpdateProviderData = {
  requestBody: Provider;
  xTenantId?: string;
};

export type UpdateProviderResponse = Response;

export type CreateProviderData = {
  requestBody: Provider;
  xTenantId?: string;
};

export type CreateProviderResponse = Response;

export type UpdateProviderOnboardingStatusData = {
  providerId: string;
  status: "PENDING" | "COMPLETED";
  xTenantId?: string;
};

export type UpdateProviderOnboardingStatusResponse = Response;

export type UpdateProviderAvatarStatusData = {
  providerId: string;
  status: "PENDING" | "IN_PROGRESS" | "READY_TO_PREVIEW" | "COMPLETED";
  xTenantId?: string;
};

export type UpdateProviderAvatarStatusResponse = Response;

export type UpdateProviderArchiveStatusData = {
  providerId: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdateProviderArchiveStatusResponse = Response;

export type UploadVideoData = {
  providerUuid: string;
  requestBody?: {
    file: Blob | File;
  };
  xTenantId?: string;
};

export type UploadVideoResponse = Response;

export type NotifyPatientData = {
  appointmentId: string;
  notifyType:
    | "PATIENT_APPOINTMENT_ACCEPTED"
    | "PATIENT_APPOINTMENT_REQUEST"
    | "BROADCAST"
    | "APPOINTMENT_REMINDER"
    | "PATIENT_MEDICATION_REMINDER"
    | "CARE_PLAN_REMINDER"
    | "PATIENT_VITAL_ALERT"
    | "SMART_ALERT_REMINDER";
  xTenantId?: string;
};

export type NotifyPatientResponse = Response;

export type ChangeAvatarData = {
  providerUuid: string;
  requestBody: ChangeAvatarRequest;
  xTenantId?: string;
};

export type ChangeAvatarResponse = Response;

export type GetProviderByIdData = {
  providerUuid: string;
  xTenantId?: string;
};

export type GetProviderByIdResponse = Response;

export type GetNurseReportDashboardData = {
  month: number;
  page?: number;
  providerId: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  xTenantId?: string;
  year: number;
};

export type GetNurseReportDashboardResponse = Response;

export type GetPatientDashboardData = {
  month: number;
  page?: number;
  providerId: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  xTenantId?: string;
  year: number;
};

export type GetPatientDashboardResponse = Response;

export type GetUserIdByProviderData = {
  userId: string;
  xTenantId?: string;
};

export type GetUserIdByProviderResponse = Response;

export type GetProfileData = {
  xTenantId?: string;
};

export type GetProfileResponse = Response;

export type DeleteVideoData = {
  providerUuid: string;
  xTenantId?: string;
};

export type DeleteVideoResponse = Response;

export type GetAllProviderGroupsData = {
  archive?: boolean;
  page?: number;
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  state?: string;
  status?: boolean;
  xTenantId?: string;
};

export type GetAllProviderGroupsResponse = Response;

export type UpdateProviderGroupData = {
  requestBody: ProviderGroup;
  xTenantId?: string;
};

export type UpdateProviderGroupResponse = Response;

export type CreateProviderGroupData = {
  requestBody: ProviderGroup;
  xTenantId?: string;
};

export type CreateProviderGroupResponse = Response;

export type SyncDatabaseSchemaData = {
  roleSync?: boolean;
  uuid: string;
  xTenantId?: string;
};

export type SyncDatabaseSchemaResponse = Response;

export type UpdateProviderGroupArchiveStatusData = {
  providerGroupId: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdateProviderGroupArchiveStatusResponse = Response;

export type UpdateProviderGroupConfigurationData = {
  requestBody: ProviderGroup;
  xTenantId?: string;
};

export type UpdateProviderGroupConfigurationResponse = Response;

export type CreateRealmData = {
  realmName: string;
  xTenantId?: string;
};

export type CreateRealmResponse = unknown;

export type ChangeAvatar1Data = {
  providerGroupId: string;
  requestBody: ChangeAvatarRequest;
  xTenantId?: string;
};

export type ChangeAvatar1Response = Response;

export type GetProviderGroupByIdData = {
  providerGroupId: string;
  xTenantId?: string;
};

export type GetProviderGroupByIdResponse = Response;

export type GetProviderGroupBySchemaData = {
  xTenantId?: string;
};

export type GetProviderGroupBySchemaResponse = Response;

export type GetAllPatientData = {
  archive?: boolean;
  genderFilter?: "MALE" | "FEMALE" | "OTHER" | "BOTH";
  mrn?: string;
  name?: string;
  nurseId?: string;
  page?: number;
  providerId?: string;
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  status?: boolean;
  xTenantId?: string;
};

export type GetAllPatientResponse = Response;

export type UpdatePatientData = {
  requestBody: Patient;
  xTenantId?: string;
};

export type UpdatePatientResponse = Response;

export type CreatePatientData = {
  isAiGenerated?: boolean;
  requestBody: Patient;
  xTenantId?: string;
};

export type CreatePatientResponse = Response;

export type UpdatePatientArchiveStatusData = {
  patientId: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdatePatientArchiveStatusResponse = Response;

export type ChangeAvatar2Data = {
  patientUuid: string;
  requestBody: ChangeAvatarRequest;
  xTenantId?: string;
};

export type ChangeAvatar2Response = Response;

export type UploadFileData = {
  formData: {
    file: Blob | File;
  };
  isAiGenerated?: boolean;
  xTenantId?: string;
};

export type UploadFileResponse = Response;

export type GetPatientByIdData = {
  patientUuid: string;
  xTenantId?: string;
};

export type GetPatientByIdResponse = Response;

export type GetPatientStatisticData = {
  patientId: string;
  xTenantId?: string;
};

export type GetPatientStatisticResponse = Response;

export type GetPatientRecordData = {
  patientId: string;
  xTenantId?: string;
};

export type GetPatientRecordResponse = Response;

export type GetProfile2Data = {
  xTenantId?: string;
};

export type GetProfile2Response = Response;

export type DownloadTemplateData = {
  xTenantId?: string;
};

export type DownloadTemplateResponse = Blob | File;

export type GetPatientListData = {
  archive?: boolean;
  nurseId?: string;
  page?: number;
  providerId?: string;
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  status?: boolean;
  xTenantId?: string;
};

export type GetPatientListResponse = Response;

export type GetAssignedDevicesData = {
  page?: number;
  patientId: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  type?: string;
  xTenantId?: string;
};

export type GetAssignedDevicesResponse = Response;

export type GetPatientVitals1Data = {
  endDate?: string;
  page?: number;
  patientUuid: string;
  size?: number;
  sort?: string;
  sortBy?: string;
  startDate?: string;
  timeFilter?: "LAST_MONTH" | "LAST_WEEK" | "PAST_24_HOURS" | "DATE_RANGE";
  vitalName?: string;
  xTenantId?: string;
};

export type GetPatientVitals1Response = Response;

export type UpdatePatientVitalData = {
  requestBody: PatientVital;
  xTenantId?: string;
};

export type UpdatePatientVitalResponse = Response;

export type CreatePatientVitalData = {
  requestBody: PatientVital;
  xTenantId?: string;
};

export type CreatePatientVitalResponse = Response;

export type SyncPatientVitalData = {
  patientEhrId: string;
  xTenantId?: string;
};

export type SyncPatientVitalResponse = Response;

export type CreateBulkPatientVitalData = {
  requestBody: PatientVitalRequest;
  xTenantId?: string;
};

export type CreateBulkPatientVitalResponse = Response;

export type GetPatientVitalByIdData = {
  patientVitalId: string;
  xTenantId?: string;
};

export type GetPatientVitalByIdResponse = Response;

export type GetPatientLatestVitalsData = {
  patientUuid: string;
  xTenantId?: string;
};

export type GetPatientLatestVitalsResponse = Response;

export type GetEcgValueData = {
  ecgId: string;
  xTenantId?: string;
};

export type GetEcgValueResponse = Response;

export type GetPatientMedicationData = {
  archive?: boolean;
  page?: number;
  patientUuid: string;
  searchString?: string;
  size?: number;
  sort?: string;
  sortBy?: string;
  status?: boolean;
  timeFilter?: "CURRENT" | "PAST";
  xTenantId?: string;
};

export type GetPatientMedicationResponse = Response;

export type UpdatePatientMedicationData = {
  requestBody: PatientMedication;
  xTenantId?: string;
};

export type UpdatePatientMedicationResponse = Response;

export type CreatePatientMedicationData = {
  requestBody: PatientMedication;
  xTenantId?: string;
};

export type CreatePatientMedicationResponse = Response;

export type UpdateBulkPatientMedicationDosageData = {
  patientId: string;
  requestBody: PatientMedication[];
  xTenantId?: string;
};

export type UpdateBulkPatientMedicationDosageResponse = Response;

export type SyncPatientMedicationData = {
  patientEhrId: string;
  xTenantId?: string;
};

export type SyncPatientMedicationResponse = Response;

export type DeletePatientMedicationIdData = {
  patientMedicationId: string;
  status?: boolean;
  xTenantId?: string;
};

export type DeletePatientMedicationIdResponse = Response;

export type CreateBulkPatientMedicationData = {
  patientId: string;
  requestBody: PatientMedication[];
  xTenantId?: string;
};

export type CreateBulkPatientMedicationResponse = Response;

export type CreateBulkPatientMedicationWithImageData = {
  patientId: string;
  requestBody?: {
    medications: string;
    images: (Blob | File)[];
  };
  xTenantId?: string;
};

export type CreateBulkPatientMedicationWithImageResponse = Response;

export type GetPatientMedicationByIdData = {
  patientMedicationId: string;
  xTenantId?: string;
};

export type GetPatientMedicationByIdResponse = Response;

export type SendPatientNotificationData = {
  xTenantId?: string;
};

export type SendPatientNotificationResponse = Response;

export type GetPatientDiagnosisData = {
  archive?: boolean;
  page?: number;
  patientUuid: string;
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  status?: boolean;
  xTenantId?: string;
};

export type GetPatientDiagnosisResponse = Response;

export type UpdatePatientDiagnosisData = {
  requestBody: PatientDiagnosis;
  xTenantId?: string;
};

export type UpdatePatientDiagnosisResponse = Response;

export type CreatePatientDiagnosisData = {
  requestBody: PatientDiagnosis;
  xTenantId?: string;
};

export type CreatePatientDiagnosisResponse = Response;

export type UpdatePatientDiagnosisArchiveStatusData = {
  patientDiagnosisId: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdatePatientDiagnosisArchiveStatusResponse = Response;

export type SyncPatientDiagnosisData = {
  patientEhrId: string;
  xTenantId?: string;
};

export type SyncPatientDiagnosisResponse = Response;

export type CreateBulkPatientDiagnosisData = {
  patientId: string;
  requestBody: PatientDiagnosis[];
  xTenantId?: string;
};

export type CreateBulkPatientDiagnosisResponse = Response;

export type GetPatientDiagnosisByIdData = {
  patientDiagnosisId: string;
  xTenantId?: string;
};

export type GetPatientDiagnosisByIdResponse = Response;

export type UpdatePatientConsentStatusData = {
  patientUuid: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdatePatientConsentStatusResponse = Response;

export type GetAllConsentFormTemplateData = {
  archive?: boolean;
  page?: number;
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  status?: boolean;
  xTenantId?: string;
};

export type GetAllConsentFormTemplateResponse = Response;

export type UpdateConsentFormsData = {
  requestBody: ConsentFormTemplate;
  xTenantId?: string;
};

export type UpdateConsentFormsResponse = Response;

export type CreateConsentFormsData = {
  requestBody: ConsentFormTemplate;
  xTenantId?: string;
};

export type CreateConsentFormsResponse = Response;

export type UpdateConsentFormArchiveStatusData = {
  consentFormId: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdateConsentFormArchiveStatusResponse = Response;

export type GetAllPatientConsentFormData = {
  page?: number;
  patientUuid: string;
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  xTenantId?: string;
};

export type GetAllPatientConsentFormResponse = Response;

export type AddPatientConsentData = {
  requestBody: PatientConsentForm;
  xTenantId?: string;
};

export type AddPatientConsentResponse = Response;

export type GetPatientConsentFormByIdData = {
  patientConsentFormUuid: string;
  xTenantId?: string;
};

export type GetPatientConsentFormByIdResponse = Response;

export type GetConsentFormIdData = {
  consentFormId: string;
  xTenantId?: string;
};

export type GetConsentFormIdResponse = Response;

export type GetAllCarePlansData = {
  archive?: boolean;
  carePlanStatus?: string;
  page?: number;
  patientId: string;
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  status?: boolean;
  timeFilter?: "CURRENT" | "PAST";
  xTenantId?: string;
};

export type GetAllCarePlansResponse = Response;

export type UpdatePatientCarePlanData = {
  requestBody: PatientCarePlanUpdateRequest;
  xTenantId?: string;
};

export type UpdatePatientCarePlanResponse = Response;

export type UpdateCarePlanArchiveStatusData = {
  patientCarePlanId: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdateCarePlanArchiveStatusResponse = Response;

export type UpdatePatientCarePlanStatusData = {
  requestBody: PatientCarePlanStatusChange;
  xTenantId?: string;
};

export type UpdatePatientCarePlanStatusResponse = Response;

export type UpdateProgramGoalTrackData = {
  requestBody: PatientProgramGoalTrack;
  xTenantId?: string;
};

export type UpdateProgramGoalTrackResponse = Response;

export type AddProgramGoalTrackData = {
  requestBody: PatientProgramGoalTrack;
  xTenantId?: string;
};

export type AddProgramGoalTrackResponse = Response;

export type UpdateVitalReferenceRangeData = {
  requestBody: VitalReference;
  xTenantId?: string;
};

export type UpdateVitalReferenceRangeResponse = Response;

export type BulkAssignCarePlansData = {
  requestBody: BulkCarePlanRequest;
  xTenantId?: string;
};

export type BulkAssignCarePlansResponse = Response;

export type AssignCarePlanData = {
  patientId: string;
  requestBody: PatientCarePlanRequest;
  xTenantId?: string;
};

export type AssignCarePlanResponse = Response;

export type GetPatientCarePlanByIdData = {
  patientCarePlanId: string;
  xTenantId?: string;
};

export type GetPatientCarePlanByIdResponse = Response;

export type GetProgramGoalTrackDetailsData = {
  programGoalId: string;
  xTenantId?: string;
};

export type GetProgramGoalTrackDetailsResponse = Response;

export type GetPatientActiveCarePlanData = {
  patientId: string;
  xTenantId?: string;
};

export type GetPatientActiveCarePlanResponse = Response;

export type GetPatientAllergyData = {
  archive?: boolean;
  page?: number;
  patientUuid: string;
  searchString?: string;
  size?: number;
  sort?: string;
  sortBy?: string;
  status?: boolean;
  type?: "DRUG" | "FOOD" | "ENVIRONMENT" | "OTHER";
  xTenantId?: string;
};

export type GetPatientAllergyResponse = Response;

export type UpdatePatientAllergyData = {
  requestBody: PatientAllergy;
  xTenantId?: string;
};

export type UpdatePatientAllergyResponse = Response;

export type CreatePatientAllergyData = {
  requestBody: PatientAllergy;
  xTenantId?: string;
};

export type CreatePatientAllergyResponse = Response;

export type UpdatePatientAllergyArchiveStatusData = {
  patientAllergyId: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdatePatientAllergyArchiveStatusResponse = Response;

export type SyncPatientAllergyData = {
  patientEhrId: string;
  xTenantId?: string;
};

export type SyncPatientAllergyResponse = Response;

export type CreateBulkPatientAllergyData = {
  patientId: string;
  requestBody: PatientAllergy[];
  xTenantId?: string;
};

export type CreateBulkPatientAllergyResponse = Response;

export type GetPatientAllergyByIdData = {
  patientAllergyId: string;
  xTenantId?: string;
};

export type GetPatientAllergyByIdResponse = Response;

export type GetMedicalCodesData = {
  active?: boolean;
  archive?: boolean;
  page?: number;
  searchString?: string;
  size?: number;
  sort?: string;
  sortBy?: string;
  type?: "ICD10" | "CPT" | "ALL";
  xTenantId?: string;
};

export type GetMedicalCodesResponse = Response;

export type UpdateMedicalCodeData = {
  requestBody: MedicalCode;
  xTenantId?: string;
};

export type UpdateMedicalCodeResponse = Response;

export type CreateMedicalCodeData = {
  requestBody: MedicalCode;
  xTenantId?: string;
};

export type CreateMedicalCodeResponse = Response;

export type UpdateMedicalCodeStatusData = {
  medicalCodeId: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdateMedicalCodeStatusResponse = Response;

export type UpdateMedicalCodeArchiveStatusData = {
  medicalCodeId: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdateMedicalCodeArchiveStatusResponse = Response;

export type UploadFile1Data = {
  category: "ICD10" | "CPT" | "ALL";
  formData: {
    file: Blob | File;
  };
  providerGroupUuid?: string;
  title: string;
  xTenantId?: string;
};

export type UploadFile1Response = Response;

export type GetMedicalCodeByIdData = {
  medicalCodeId: string;
  xTenantId?: string;
};

export type GetMedicalCodeByIdResponse = Response;

export type GetAllLocationsData = {
  archive?: boolean;
  page?: number;
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  state?: string;
  status?: boolean;
  xTenantId?: string;
};

export type GetAllLocationsResponse = Response;

export type UpdateLocationData = {
  requestBody: Location;
  xTenantId?: string;
};

export type UpdateLocationResponse = Response;

export type CreateLocationData = {
  requestBody: Location;
  xTenantId?: string;
};

export type CreateLocationResponse = Response;

export type UpdateLocationArchiveStatusData = {
  locationId: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdateLocationArchiveStatusResponse = Response;

export type GetLocationByIdData = {
  locationId: string;
  xTenantId?: string;
};

export type GetLocationByIdResponse = Response;

export type GetAllDevicesData = {
  archive?: boolean;
  category?: string;
  page?: number;
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  status?: boolean;
  type?: string;
  xTenantId?: string;
};

export type GetAllDevicesResponse = Response;

export type UpdateDeviceData = {
  requestBody: Device;
  xTenantId?: string;
};

export type UpdateDeviceResponse = Response;

export type CreateDeviceData = {
  requestBody: Device;
  xTenantId?: string;
};

export type CreateDeviceResponse = Response;

export type UpdateDeviceStatusData = {
  deviceUuid: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdateDeviceStatusResponse = Response;

export type UpdateDeviceArchiveStatusData = {
  deviceUuid: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdateDeviceArchiveStatusResponse = Response;

export type AssignDeviceData = {
  xTenantId?: string;
};

export type AssignDeviceResponse = Response;

export type GetDeviceByIdData = {
  deviceUuid: string;
  xTenantId?: string;
};

export type GetDeviceByIdResponse = Response;

export type UpdateClinicalNoteData = {
  requestBody: ClinicalNote;
  signOff?: boolean;
  xTenantId?: string;
};

export type UpdateClinicalNoteResponse = Response;

export type CreateClinicalNoteData = {
  requestBody: ClinicalNote;
  xTenantId?: string;
};

export type CreateClinicalNoteResponse = Response;

export type ExportAndEmailPdfData = {
  clinicalNoteUuid: string;
  xTenantId?: string;
};

export type ExportAndEmailPdfResponse = Response;

export type GetClinicalNoteByUuidData = {
  clinicalNoteUuid: string;
  xTenantId?: string;
};

export type GetClinicalNoteByUuidResponse = Response;

export type GetClinicalNoteByAppointmentIdData = {
  appointmentId: string;
  xTenantId?: string;
};

export type GetClinicalNoteByAppointmentIdResponse = Response;

export type GetAllCarePlans1Data = {
  archive?: boolean;
  page?: number;
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  status?: boolean;
  xTenantId?: string;
};

export type GetAllCarePlans1Response = Response;

export type UpdateCarePlanData = {
  requestBody: CarePlan;
  xTenantId?: string;
};

export type UpdateCarePlanResponse = Response;

export type CreateCarePlanData = {
  requestBody: CarePlan;
  xTenantId?: string;
};

export type CreateCarePlanResponse = Response;

export type UpdateUserStatusData = {
  carePlanId: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdateUserStatusResponse = Response;

export type UpdateCarePlanArchiveStatus1Data = {
  carePlanId: string;
  status: boolean;
  xTenantId?: string;
};

export type UpdateCarePlanArchiveStatus1Response = Response;

export type GetCarePlanByIdData = {
  carePlanId: string;
  globalCarePlan?: boolean;
  xTenantId?: string;
};

export type GetCarePlanByIdResponse = Response;

export type GetAllReferenceRangesData = {
  xTenantId?: string;
};

export type GetAllReferenceRangesResponse = Response;

export type GetAllProtocolsData = {
  protocolType: "OUT_OF_RANGE_BP" | "OUT_OF_RANGE_HR" | "OUT_OF_RANGE_BG";
  xTenantId?: string;
};

export type GetAllProtocolsResponse = Response;

export type GetAllAppointmentsData = {
  assigned?: boolean;
  endDate?: string;
  filter?: "ALL" | "UPCOMING" | "PAST" | "REQUESTED";
  mode?: string;
  nurseId?: string;
  page?: number;
  patientId?: string;
  providerId?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  startDate?: string;
  status?: string;
  xTenantId?: string;
};

export type GetAllAppointmentsResponse = Response;

export type UpdateAppointmentData = {
  requestBody: Appointment;
  xTenantId?: string;
};

export type UpdateAppointmentResponse = Response;

export type CreateAppointmentData = {
  requestBody: Appointment;
  xTenantId?: string;
};

export type CreateAppointmentResponse = Response;

export type UpdateAppointmentStatusData = {
  requestBody: AppointmentStatusChange;
  xTenantId?: string;
};

export type UpdateAppointmentStatusResponse = Response;

export type RescheduleAppointmentData = {
  requestBody: RescheduleRequest;
  xTenantId?: string;
};

export type RescheduleAppointmentResponse = Response;

export type BroadCastAppointmentData = {
  requestBody: AppointmentStatusChange;
  xTenantId?: string;
};

export type BroadCastAppointmentResponse = Response;

export type BookAppointmentRequestData = {
  requestBody: AppointmentRequest;
  xTenantId?: string;
};

export type BookAppointmentRequestResponse = Response;

export type SendInvitationLinkData = {
  requestBody: UserInvitationRequest;
  xTenantId?: string;
};

export type SendInvitationLinkResponse = Response;

export type GetAppointmentByIdData = {
  appointmentId: string;
  xTenantId?: string;
};

export type GetAppointmentByIdResponse = Response;

export type EscalateAppointmentData = {
  email: string;
  schema?: string;
  xTenantId?: string;
};

export type EscalateAppointmentResponse = Response;

export type GetAppointmentListData = {
  endDate: string;
  filter?: "ALL" | "UPCOMING" | "PAST" | "REQUESTED";
  nurseId?: string;
  patientUuid?: string;
  providerId?: string;
  startDate: string;
  status?: string;
  type?: string;
  xTenantId?: string;
};

export type GetAppointmentListResponse = Response;

export type SetProviderAvailabilitySettingData = {
  requestBody: AvailabilitySetting;
  xTenantId?: string;
};

export type SetProviderAvailabilitySettingResponse = Response;

export type GetProviderSlotsData = {
  duration: number;
  endDate?: string;
  page?: number;
  providerUuid: string;
  size?: number;
  startDate?: string;
  xTenantId?: string;
};

export type GetProviderSlotsResponse = Response;

export type GetProviderAvailabilitySettingData = {
  providerUuid: string;
  xTenantId?: string;
};

export type GetProviderAvailabilitySettingResponse = Response;

export type AddUpdateTrainedDeviceData = {
  requestBody: PatientDeviceRequest;
  xTenantId?: string;
};

export type AddUpdateTrainedDeviceResponse = Response;

export type SendDirectedMessageData = {
  requestBody: SendDirectMessageRequest;
  xTenantId?: string;
};

export type SendDirectedMessageResponse = Response;

export type TestNotifData = {
  requestBody: string[];
  uuid: string;
  xTenantId?: string;
};

export type TestNotifResponse = Response;

export type TestNotifImageData = {
  image: string;
  requestBody: string[];
  xTenantId?: string;
};

export type TestNotifImageResponse = Response;

export type SendMulticastMessageData = {
  requestBody: MessageRequest;
  xTenantId?: string;
};

export type SendMulticastMessageResponse = string;

export type SaveTokenData = {
  fcmToken: string;
  xTenantId?: string;
};

export type SaveTokenResponse = Response;

export type DeleteTokenData = {
  fcmToken: string;
  xTenantId?: string;
};

export type DeleteTokenResponse = Response;

export type StoreNurseActionStatisticsData = {
  xTenantId?: string;
};

export type StoreNurseActionStatisticsResponse = Response;

export type AvqSetupAvatarData = {
  requestBody?: {
    request: string;
    audio_files: (Blob | File)[];
  };
  xTenantId?: string;
};

export type AvqSetupAvatarResponse = Response;

export type AvqSetupAvatarEdgeData = {
  requestBody: GenerateAvatarRequest;
  xTenantId?: string;
};

export type AvqSetupAvatarEdgeResponse = Response;

export type SendPatientAlertData = {
  xTenantId?: string;
};

export type SendPatientAlertResponse = Response;

export type LlmauGenerateMedicationAudioData = {
  requestBody: MedicationAudioRequest;
  xTenantId?: string;
};

export type LlmauGenerateMedicationAudioResponse = Response;

export type VygenGenerateAvatarData = {
  requestBody: GenerateAvatarRequest;
  xTenantId?: string;
};

export type VygenGenerateAvatarResponse = Response;

export type GenerateAudioMonthlyQuickSummaryData = {
  xTenantId?: string;
};

export type GenerateAudioMonthlyQuickSummaryResponse = Response;

export type VygenDetectFaceData = {
  requestBody: GenerateAvatarRequest;
  xTenantId?: string;
};

export type VygenDetectFaceResponse = Response;

export type LlmauChatWithMyNurseData = {
  providerId: string;
  requestBody: Message;
  xTenantId?: string;
};

export type LlmauChatWithMyNurseResponse = Response;

export type GetAiCarePlanData = {
  requestBody: PatientRequest;
  xTenantId?: string;
};

export type GetAiCarePlanResponse = Response;

export type GetAiCarePlanListData = {
  requestBody: PatientDTO;
  xTenantId?: string;
};

export type GetAiCarePlanListResponse = Response;

export type LlmauAudioChatWithMyNurseData = {
  providerId: string;
  requestBody?: {
    timezone?: string;
    audio_files: Blob | File;
  };
  xTenantId?: string;
};

export type LlmauAudioChatWithMyNurseResponse = Response;

export type LlmauAnalyzeMedicationData = {
  requestBody?: {
    images: (Blob | File)[];
  };
  xTenantId?: string;
};

export type LlmauAnalyzeMedicationResponse = Response;

export type GetPatientReportsData = {
  month?: number;
  page?: number;
  patientUuid: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  xTenantId?: string;
  year?: number;
};

export type GetPatientReportsResponse = Response;

export type LlmauGetVoiceRecommendationData = {
  xTenantId?: string;
};

export type LlmauGetVoiceRecommendationResponse = Response;

export type GetNurseReportsData = {
  reportUuid: string;
  xTenantId?: string;
};

export type GetNurseReportsResponse = Response;

export type LlmauGetMyNurseAvatarData = {
  providerId: string;
  xTenantId?: string;
};

export type LlmauGetMyNurseAvatarResponse = Response;

export type GetNurseActionStatisticsData = {
  month: number;
  patientUuid: string;
  xTenantId?: string;
  year: number;
};

export type GetNurseActionStatisticsResponse = Response;

export type LlmauGetAvatarData = {
  xTenantId?: string;
};

export type LlmauGetAvatarResponse = Response;

export type GetNurseActionInExcelData = {
  month: number;
  patientUuid: string;
  xTenantId?: string;
  year: number;
};

export type GetNurseActionInExcelResponse = Response;

export type GetChatbotReportsData = {
  month: number;
  patientUuid: string;
  xTenantId?: string;
  year: number;
};

export type GetChatbotReportsResponse = Response;

export type GetChatbotHistoryData = {
  xTenantId?: string;
};

export type GetChatbotHistoryResponse = Response;

export type LlmauGetAvatarVideoByTitleData = {
  providerId: string;
  videoTitle: string;
  xTenantId?: string;
};

export type LlmauGetAvatarVideoByTitleResponse = Response;

export type AvqGetAlertAudioData = {
  providerId: string;
  videoTitle: string;
  xTenantId?: string;
};

export type AvqGetAlertAudioResponse = Response;

export type GetPatientVitalsData = {
  page?: number;
  searchString?: string;
  size?: number;
  sort?: string;
  sortBy?: string;
  xTenantId?: string;
};

export type GetPatientVitalsResponse = Response;

export type GetAuthTokenData = {
  roomId: string;
  xTenantId?: string;
};

export type GetAuthTokenResponse = Response;

export type SubscribeData = {
  eventKey: string;
  xTenantId?: string;
};

export type SubscribeResponse = SseEmitter;

export type EmitData = {
  eventKey: string;
  xTenantId?: string;
};

export type EmitResponse = Response;

export type GetAllLicensedStatesData = {
  page?: number;
  searchString?: string;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
  xTenantId?: string;
};

export type GetAllLicensedStatesResponse = Response;

export type GetInfusionTherapyByIdData = {
  therapyId: string;
  xTenantId?: string;
};

export type GetInfusionTherapyByIdResponse = Response;

export type GetAllEhrProvidersData = {
  xTenantId?: string;
};

export type GetAllEhrProvidersResponse = Response;

export type GetPractitionerByProviderIdData = {
  practitionerId: string;
  xTenantId?: string;
};

export type GetPractitionerByProviderIdResponse = Provider;

export type SearchPractitionersData = {
  name: string;
  xTenantId?: string;
};

export type SearchPractitionersResponse = Provider[];

export type GetPatientVitals2Data = {
  date?: string;
  patientId: string;
  xTenantId?: string;
};

export type GetPatientVitals2Response = PatientVital[];

export type GetPatientEncounterDiagnosisByPatientIdData = {
  patientId: string;
  xTenantId?: string;
};

export type GetPatientEncounterDiagnosisByPatientIdResponse = PatientDiagnosis[];

export type GetAllergiesByPatientIdData = {
  clinicalStatus?: string;
  patientId: string;
  recordedDate?: string;
  xTenantId?: string;
};

export type GetAllergiesByPatientIdResponse = PatientAllergy[];

export type SearchPatientsData = {
  birthdate?: string;
  family?: string;
  given?: string;
  organisationId: string;
  patientId?: string;
  xTenantId?: string;
};

export type SearchPatientsResponse = Patient[];

export type GetOrganizationByPracticeIdData = {
  practiceId: string;
  xTenantId?: string;
};

export type GetOrganizationByPracticeIdResponse = ProviderGroup;

export type GetMedicationRequestByPatientIdData = {
  patientId: string;
  status?: string;
  xTenantId?: string;
};

export type GetMedicationRequestByPatientIdResponse = {
  [key: string]: unknown;
};

export type GetMedicationDispenseByPatientIdData = {
  patientId: string;
  status?: string;
  whenhandedover?: string;
  xTenantId?: string;
};

export type GetMedicationDispenseByPatientIdResponse = {
  [key: string]: unknown;
};

export type GetLocationByLocationIdData = {
  locationId: string;
  xTenantId?: string;
};

export type GetLocationByLocationIdResponse = Location;

export type GetAccessToken1Data = {
  xTenantId?: string;
};

export type GetAccessToken1Response = EhrAccessToken;

export type GetAllConditionsData = {
  name?: string;
  page?: number;
  size?: number;
  sort?: string;
  sortBy?: string;
  xTenantId?: string;
};

export type GetAllConditionsResponse = Response;

export type GetAllActivitiesData = {
  search?: string;
  xTenantId?: string;
};

export type GetAllActivitiesResponse = Response;

export type $OpenApiTs = {
  "/api/master/{userId}/archive-status/{status}": {
    put: {
      req: UpdateUserArchiveStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/user": {
    get: {
      req: GetAllUsersData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateUserData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: AddUserData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/change-avatar/{userUuid}": {
    put: {
      req: ChangeAvatar3Data;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/verify-user/{email}": {
    post: {
      req: VerifyUserData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/verify-otp/{linkType}/{email}/{otp}": {
    post: {
      req: VerifyOtpData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/set-password/{linkType}": {
    post: {
      req: SetPasswordData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/resend-otp/{linkType}/{email}": {
    post: {
      req: ResendOtpData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/logout": {
    post: {
      req: LogoutData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/login": {
    post: {
      req: GetAccessTokenData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/change-password": {
    post: {
      req: ChangePasswordData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/access-token": {
    post: {
      req: GetAccessTokenFromRefreshTokenData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/user/{userId}": {
    get: {
      req: GetUserData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/profile": {
    get: {
      req: GetProfile1Data;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/vital-setting/{patientId}/setting-name/{settingName}/{status}": {
    put: {
      req: UpdatePatientVitalSettingData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/vital-setting": {
    get: {
      req: GetVitalSettingsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/vital-setting/patient/{patientUuid}": {
    get: {
      req: GetPatientVitalSettingData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/time-log": {
    get: {
      req: GetAllPatientTimeLogsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateTimeLogData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreateTimeLogAsyncData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/time-log/player-time-log": {
    post: {
      req: PlayerTimeLogData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/time-log/{timeLogId}": {
    get: {
      req: GetTimeLogByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    delete: {
      req: DeleteTimeLogByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/task": {
    get: {
      req: GetAllTasksData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateTaskData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: AddTaskData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/task/{taskUuid}/status/{status}": {
    put: {
      req: UpdateTaskStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/task/{taskUuid}": {
    get: {
      req: GetTaskByUuidData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/task/{taskUuid}/archive-status/{status}": {
    delete: {
      req: ArchiveTaskData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/role": {
    get: {
      req: GetAllRolesData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateRoleData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: AddRoleData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/role/update-role-permission": {
    put: {
      req: AddUpdateRolePermissionsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/role/privileges": {
    get: {
      req: GetAllPrivilegesData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateRolePermissionData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/role/privileges/reset": {
    post: {
      req: ResetAllRolePrivilegesData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/role/{realm}/permissions": {
    get: {
      req: GetAllRolesPermissionsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider": {
    get: {
      req: GetAllProvidersData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateProviderData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreateProviderData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/{providerId}/onboarding-status/{status}": {
    put: {
      req: UpdateProviderOnboardingStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/{providerId}/avatar-status/{status}": {
    put: {
      req: UpdateProviderAvatarStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/{providerId}/archive-status/{status}": {
    put: {
      req: UpdateProviderArchiveStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/upload": {
    put: {
      req: UploadVideoData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/notify/patient/{appointmentId}": {
    put: {
      req: NotifyPatientData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/change-avatar/{providerUuid}": {
    put: {
      req: ChangeAvatarData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/{providerUuid}": {
    get: {
      req: GetProviderByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/{providerId}/reports/dashboard": {
    get: {
      req: GetNurseReportDashboardData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/{providerId}/patient/dashboard": {
    get: {
      req: GetPatientDashboardData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/user/{userId}": {
    get: {
      req: GetUserIdByProviderData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/profile": {
    get: {
      req: GetProfileData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/video/{providerUuid}": {
    delete: {
      req: DeleteVideoData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider-group": {
    get: {
      req: GetAllProviderGroupsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateProviderGroupData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreateProviderGroupData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider-group/{uuid}/sync": {
    put: {
      req: SyncDatabaseSchemaData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider-group/{providerGroupId}/archive-status/{status}": {
    put: {
      req: UpdateProviderGroupArchiveStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider-group/update-configuration": {
    put: {
      req: UpdateProviderGroupConfigurationData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider-group/realm/{realmName}": {
    put: {
      req: CreateRealmData;
      res: {
        /**
         * OK
         */
        200: unknown;
      };
    };
  };
  "/api/master/provider-group/change-avatar/{providerGroupId}": {
    put: {
      req: ChangeAvatar1Data;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider-group/{providerGroupId}": {
    get: {
      req: GetProviderGroupByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider-group/profile": {
    get: {
      req: GetProviderGroupBySchemaData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient": {
    get: {
      req: GetAllPatientData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdatePatientData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreatePatientData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient/{patientId}/archive-status/{status}": {
    put: {
      req: UpdatePatientArchiveStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient/change-avatar/{patientUuid}": {
    put: {
      req: ChangeAvatar2Data;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient/patient-upload": {
    post: {
      req: UploadFileData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient/{patientUuid}": {
    get: {
      req: GetPatientByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient/statistic/{patientId}": {
    get: {
      req: GetPatientStatisticData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient/record/{patientId}": {
    get: {
      req: GetPatientRecordData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient/profile": {
    get: {
      req: GetProfile2Data;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient/patient-template": {
    get: {
      req: DownloadTemplateData;
      res: {
        /**
         * OK
         */
        200: Blob | File;
      };
    };
  };
  "/api/master/patient/list": {
    get: {
      req: GetPatientListData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient/devices/{patientId}": {
    get: {
      req: GetAssignedDevicesData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-vital": {
    get: {
      req: GetPatientVitals1Data;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdatePatientVitalData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreatePatientVitalData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-vital/sync/{patientEhrId}": {
    put: {
      req: SyncPatientVitalData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-vital/list": {
    post: {
      req: CreateBulkPatientVitalData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-vital/{patientVitalId}": {
    get: {
      req: GetPatientVitalByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-vital/latest": {
    get: {
      req: GetPatientLatestVitalsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-vital/ecg-value": {
    get: {
      req: GetEcgValueData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-medication": {
    get: {
      req: GetPatientMedicationData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdatePatientMedicationData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreatePatientMedicationData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-medication/update-bulk/{patientId}": {
    put: {
      req: UpdateBulkPatientMedicationDosageData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-medication/sync/{patientEhrId}": {
    put: {
      req: SyncPatientMedicationData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-medication/archive-status/{patientMedicationId}": {
    put: {
      req: DeletePatientMedicationIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-medication/bulk/{patientId}": {
    post: {
      req: CreateBulkPatientMedicationData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-medication/bulk-with-image/{patientId}": {
    post: {
      req: CreateBulkPatientMedicationWithImageData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-medication/{patientMedicationId}": {
    get: {
      req: GetPatientMedicationByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-medication/notification": {
    get: {
      req: SendPatientNotificationData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-diagnosis": {
    get: {
      req: GetPatientDiagnosisData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdatePatientDiagnosisData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreatePatientDiagnosisData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-diagnosis/{patientDiagnosisId}/archive-status/{status}": {
    put: {
      req: UpdatePatientDiagnosisArchiveStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-diagnosis/sync/{patientEhrId}": {
    put: {
      req: SyncPatientDiagnosisData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-diagnosis/bulk/{patientId}": {
    post: {
      req: CreateBulkPatientDiagnosisData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-diagnosis/{patientDiagnosisId}": {
    get: {
      req: GetPatientDiagnosisByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-consent-form/{patientUuid}/status/{status}": {
    put: {
      req: UpdatePatientConsentStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/consent-form": {
    get: {
      req: GetAllConsentFormTemplateData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateConsentFormsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreateConsentFormsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/consent-form/{consentFormId}/archive-status/{status}": {
    put: {
      req: UpdateConsentFormArchiveStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-consent-form": {
    get: {
      req: GetAllPatientConsentFormData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: AddPatientConsentData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-consent-form/{patientConsentFormUuid}": {
    get: {
      req: GetPatientConsentFormByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/consent-form/{consentFormId}": {
    get: {
      req: GetConsentFormIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-care-plan": {
    get: {
      req: GetAllCarePlansData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdatePatientCarePlanData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-care-plan/{patientCarePlanId}/archive-status/{status}": {
    put: {
      req: UpdateCarePlanArchiveStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-care-plan/update-status": {
    put: {
      req: UpdatePatientCarePlanStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-care-plan/track-goals": {
    put: {
      req: UpdateProgramGoalTrackData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: AddProgramGoalTrackData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-care-plan/reference-range": {
    put: {
      req: UpdateVitalReferenceRangeData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-care-plan/bulk-assign": {
    put: {
      req: BulkAssignCarePlansData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-care-plan/assign/{patientId}": {
    put: {
      req: AssignCarePlanData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-care-plan/{patientCarePlanId}": {
    get: {
      req: GetPatientCarePlanByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-care-plan/track-goals/{programGoalId}": {
    get: {
      req: GetProgramGoalTrackDetailsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-care-plan/active/{patientId}": {
    get: {
      req: GetPatientActiveCarePlanData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-allergy": {
    get: {
      req: GetPatientAllergyData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdatePatientAllergyData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreatePatientAllergyData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-allergy/{patientAllergyId}/archive-status/{status}": {
    put: {
      req: UpdatePatientAllergyArchiveStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-allergy/sync/{patientEhrId}": {
    put: {
      req: SyncPatientAllergyData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-allergy/bulk/{patientId}": {
    post: {
      req: CreateBulkPatientAllergyData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-allergy/{patientAllergyId}": {
    get: {
      req: GetPatientAllergyByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/medical-codes": {
    get: {
      req: GetMedicalCodesData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateMedicalCodeData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreateMedicalCodeData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/medical-codes/{medicalCodeId}/status/{status}": {
    put: {
      req: UpdateMedicalCodeStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/medical-codes/{medicalCodeId}/archive-status/{status}": {
    put: {
      req: UpdateMedicalCodeArchiveStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/medical-codes/{category}/upload": {
    post: {
      req: UploadFile1Data;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/medical-codes/{medicalCodeId}": {
    get: {
      req: GetMedicalCodeByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/location": {
    get: {
      req: GetAllLocationsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateLocationData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreateLocationData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/location/{locationId}/archive-status/{status}": {
    put: {
      req: UpdateLocationArchiveStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/location/{locationId}": {
    get: {
      req: GetLocationByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/device": {
    get: {
      req: GetAllDevicesData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateDeviceData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreateDeviceData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/device/{deviceUuid}/status/{status}": {
    put: {
      req: UpdateDeviceStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/device/{deviceUuid}/archive-status/{status}": {
    put: {
      req: UpdateDeviceArchiveStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/device/assign/{deviceId}": {
    put: {
      req: AssignDeviceData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/device/{deviceUuid}": {
    get: {
      req: GetDeviceByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/clinical-note": {
    put: {
      req: UpdateClinicalNoteData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreateClinicalNoteData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/clinical-note/{clinicalNoteUuid}/export-pdf": {
    post: {
      req: ExportAndEmailPdfData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/clinical-note/{clinicalNoteUuid}": {
    get: {
      req: GetClinicalNoteByUuidData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/clinical-note/appointment/{appointmentId}": {
    get: {
      req: GetClinicalNoteByAppointmentIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/care-plan": {
    get: {
      req: GetAllCarePlans1Data;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateCarePlanData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreateCarePlanData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/care-plan/{carePlanId}/status/{status}": {
    put: {
      req: UpdateUserStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/care-plan/{carePlanId}/archive-status/{status}": {
    put: {
      req: UpdateCarePlanArchiveStatus1Data;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/care-plan/{carePlanId}": {
    get: {
      req: GetCarePlanByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/care-plan/reference-ranges": {
    get: {
      req: GetAllReferenceRangesData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/care-plan/protocols/{protocolType}": {
    get: {
      req: GetAllProtocolsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/appointment": {
    get: {
      req: GetAllAppointmentsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    put: {
      req: UpdateAppointmentData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
    post: {
      req: CreateAppointmentData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/appointment/update-status": {
    put: {
      req: UpdateAppointmentStatusData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/appointment/reschedule": {
    put: {
      req: RescheduleAppointmentData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/appointment/broadcast": {
    put: {
      req: BroadCastAppointmentData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/appointment/request": {
    post: {
      req: BookAppointmentRequestData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/appointment/invite-guest": {
    post: {
      req: SendInvitationLinkData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/appointment/{appointmentId}": {
    get: {
      req: GetAppointmentByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/appointment/escalate": {
    get: {
      req: EscalateAppointmentData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/appointment/calendar": {
    get: {
      req: GetAppointmentListData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/availability-setting": {
    post: {
      req: SetProviderAvailabilitySettingData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/{providerUuid}/slots/{duration}": {
    get: {
      req: GetProviderSlotsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/provider/{providerUuid}/availability-setting": {
    get: {
      req: GetProviderAvailabilitySettingData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/patient-training": {
    post: {
      req: AddUpdateTrainedDeviceData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/notification": {
    post: {
      req: SendDirectedMessageData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/notification/test-notif/{uuid}": {
    post: {
      req: TestNotifData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/notification/test-notif-with-image": {
    post: {
      req: TestNotifImageData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/message/send": {
    post: {
      req: SendMulticastMessageData;
      res: {
        /**
         * OK
         */
        200: string;
      };
    };
  };
  "/api/master/message/save": {
    post: {
      req: SaveTokenData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/message/remove": {
    delete: {
      req: DeleteTokenData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/store-nurse-action-statistic": {
    post: {
      req: StoreNurseActionStatisticsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/setup-avatar": {
    post: {
      req: AvqSetupAvatarData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/setup-avatar-edge": {
    post: {
      req: AvqSetupAvatarEdgeData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/notification-alert": {
    post: {
      req: SendPatientAlertData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/medication-audio": {
    post: {
      req: LlmauGenerateMedicationAudioData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/generate-avatar": {
    post: {
      req: VygenGenerateAvatarData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/generate-audio-nurse-action-statistic": {
    post: {
      req: GenerateAudioMonthlyQuickSummaryData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/detect-face": {
    post: {
      req: VygenDetectFaceData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/chat-my-nurse/{providerId}": {
    post: {
      req: LlmauChatWithMyNurseData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/care_plan_suggester": {
    post: {
      req: GetAiCarePlanData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/bulk_care_plan_suggester": {
    post: {
      req: GetAiCarePlanListData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/audio-chat-my-nurse/{providerId}": {
    post: {
      req: LlmauAudioChatWithMyNurseData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/analyze-medication": {
    post: {
      req: LlmauAnalyzeMedicationData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/patient-reports/{patientUuid}": {
    get: {
      req: GetPatientReportsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/nurse-voice-recommendation": {
    get: {
      req: LlmauGetVoiceRecommendationData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/nurse-reports/{reportUuid}": {
    get: {
      req: GetNurseReportsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/nurse-avatar/{providerId}": {
    get: {
      req: LlmauGetMyNurseAvatarData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/nurse-action-statistic/{patientUuid}": {
    get: {
      req: GetNurseActionStatisticsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/my-avatar": {
    get: {
      req: LlmauGetAvatarData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/excel-nurse-action-download/{patientUuid}": {
    get: {
      req: GetNurseActionInExcelData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/chatbot-reports/{patientUuid}": {
    get: {
      req: GetChatbotReportsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/chatbot-message-history": {
    get: {
      req: GetChatbotHistoryData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/avatar-video/{providerId}": {
    get: {
      req: LlmauGetAvatarVideoByTitleData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ai_connection/alert-audio/{providerId}": {
    get: {
      req: AvqGetAlertAudioData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/vital": {
    get: {
      req: GetPatientVitalsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/token/{roomId}": {
    get: {
      req: GetAuthTokenData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/event/subscribe/{eventKey}": {
    get: {
      req: SubscribeData;
      res: {
        /**
         * OK
         */
        200: SseEmitter;
      };
    };
  };
  "/api/master/event/emit/{eventKey}": {
    get: {
      req: EmitData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/license-state": {
    get: {
      req: GetAllLicensedStatesData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/eqr/{therapyId}": {
    get: {
      req: GetInfusionTherapyByIdData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ehr": {
    get: {
      req: GetAllEhrProvidersData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/ehr-practitioner/{practitionerId}": {
    get: {
      req: GetPractitionerByProviderIdData;
      res: {
        /**
         * OK
         */
        200: Provider;
      };
    };
  };
  "/api/master/ehr-practitioner/search": {
    get: {
      req: SearchPractitionersData;
      res: {
        /**
         * OK
         */
        200: Provider[];
      };
    };
  };
  "/api/master/ehr-patient/{patientId}/vital-sign": {
    get: {
      req: GetPatientVitals2Data;
      res: {
        /**
         * OK
         */
        200: PatientVital[];
      };
    };
  };
  "/api/master/ehr-patient/{patientId}/encounter-diagnosis": {
    get: {
      req: GetPatientEncounterDiagnosisByPatientIdData;
      res: {
        /**
         * OK
         */
        200: PatientDiagnosis[];
      };
    };
  };
  "/api/master/ehr-patient/{patientId}/allergies": {
    get: {
      req: GetAllergiesByPatientIdData;
      res: {
        /**
         * OK
         */
        200: PatientAllergy[];
      };
    };
  };
  "/api/master/ehr-patient/search": {
    get: {
      req: SearchPatientsData;
      res: {
        /**
         * OK
         */
        200: Patient[];
      };
    };
  };
  "/api/master/ehr-organization/{practiceId}": {
    get: {
      req: GetOrganizationByPracticeIdData;
      res: {
        /**
         * OK
         */
        200: ProviderGroup;
      };
    };
  };
  "/api/master/ehr-medication-request/{patientId}": {
    get: {
      req: GetMedicationRequestByPatientIdData;
      res: {
        /**
         * OK
         */
        200: {
          [key: string]: unknown;
        };
      };
    };
  };
  "/api/master/ehr-medication-dispense/{patientId}": {
    get: {
      req: GetMedicationDispenseByPatientIdData;
      res: {
        /**
         * OK
         */
        200: {
          [key: string]: unknown;
        };
      };
    };
  };
  "/api/master/ehr-location/{locationId}": {
    get: {
      req: GetLocationByLocationIdData;
      res: {
        /**
         * OK
         */
        200: Location;
      };
    };
  };
  "/api/master/ehr-access-token": {
    get: {
      req: GetAccessToken1Data;
      res: {
        /**
         * OK
         */
        200: EhrAccessToken;
      };
    };
  };
  "/api/master/condition": {
    get: {
      req: GetAllConditionsData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
  "/api/master/activity": {
    get: {
      req: GetAllActivitiesData;
      res: {
        /**
         * OK
         */
        200: Response;
      };
    };
  };
};
