import { Navigate, Outlet, createBrowserRouter } from "react-router-dom";

import CareplanForm from "@/components/admin-portal/settings/care-plan/careplan-form";

import ProviderGroupTabs from "../components/admin-portal/provider-group/provider-group-tabs";
import AuthLayout from "../layouts/auth-layout";
import MainLayout from "../layouts/main-layout";
import ProviderGroupPage from "../pages/admin-portal/provider-group/provider-group-page";
import AdminSettingsMasterPage from "../pages/admin-portal/settings/admin-settings-master";
import ProfilePage from "../pages/admin-portal/settings/profile-page";
import Login from "../pages/auth/login";
import SetPasswordPage from "../pages/auth/set-new-password";
import VerifyEmailPage from "../pages/auth/verify-email";
import VerifyOtpPage from "../pages/auth/verify-otp";
import NotAuthorized from "../pages/error/not-authorised";
import NotFound from "../pages/error/not-found";
import PrivateRoute from "./private-route";
import PublicRoute from "./public-route";

export const router = createBrowserRouter([
  { path: "", element: <Navigate to={"auth/login"} /> },
  {
    path: "auth",
    element: (
      <PublicRoute>
        <AuthLayout>
          <Outlet />
        </AuthLayout>
      </PublicRoute>
    ),
    children: [
      { path: "login", element: <Login /> },
      { path: "verify-email", element: <VerifyEmailPage /> },
      { path: "set-password", element: <SetPasswordPage /> },
      { path: "verify-otp", element: <VerifyOtpPage /> },
    ],
  },
  {
    path: "admin",
    element: (
      <PrivateRoute>
        <MainLayout>
          <Outlet />
        </MainLayout>
      </PrivateRoute>
    ),
    children: [
      {
        path: "provider-group",
        element: <Outlet />,
        children: [
          { path: "", element: <ProviderGroupPage /> },
          {
            path: ":providerGroupId",
            element: <ProviderGroupTabs />,
          },
        ],
      },
      {
        path: "settings",
        element: <Outlet />,
        children: [
          { path: "", element: <AdminSettingsMasterPage /> },
          {
            path: ":providerGroupId",
            element: <ProviderGroupTabs />,
          },
          { path: "careplan", element: <CareplanForm /> },
          {
            path: "profile",
            element: <ProfilePage />,
          },
        ],
      },
    ],
  },
  {
    path: "/not-authorized",
    element: <NotAuthorized />,
  },
  {
    path: "*",
    element: <NotFound />,
  },
]);
