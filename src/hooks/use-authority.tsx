import { useLocation } from "react-router-dom";
import { ENV } from "../constants/config";
import { Roles, RolesPortalMap } from "../constants/constants";
import { ENVIRONMENTS } from "../constants/environments";
import { Portals } from "../constants/portals";
import storageService from "../services/core/storage-service";

type AuthorityInfo = {
  hasRouteAuthority: boolean;
  portal: string;
  role: string | null;
  token: string | null;
  isSuperAdmin: boolean;
  isProvider: boolean;
  isAdminPortal: boolean;
  isProviderPortal: boolean;
  isEnvDevelopment: boolean;
  isEnvProduction: boolean;
  isEnvStage: boolean;
  isEnvQa: boolean;
};

const useAuthority = () => {
  const role = storageService.getRoles();
  const token = storageService.getToken();
  const location = useLocation();
  const pathArr = location.pathname
    ?.trim()
    .split("/")
    .filter((path) => path.length);
  const pathPrefix = pathArr[0];

  const isAdminPortal = pathPrefix === Portals.admin;
  const isProviderPortal = pathPrefix === Portals.provider;

  // Compute respective user portal based on role
  const portal = (role && RolesPortalMap[role.toString()]) || "";
  // Compute role flags
  const isSuperAdmin = role === Roles.SUPER_ADMIN;

  const isEnvDevelopment = ENV === ENVIRONMENTS.DEVELOPMENT;
  const isEnvProduction = ENV === ENVIRONMENTS.PRODUCTION;
  const isEnvStage = ENV === ENVIRONMENTS.STAGE;
  const isEnvQa = ENV === ENVIRONMENTS.QA;

  const authorityInfo: AuthorityInfo = {
    hasRouteAuthority: pathPrefix === portal,
    portal,
    role,
    token,
    isSuperAdmin,
    isAdminPortal,
    isProviderPortal,
    isEnvDevelopment,
    isEnvProduction,
    isEnvStage,
    isEnvQa,
    isProvider: false,
  };

  return authorityInfo;
};

export default useAuthority;
