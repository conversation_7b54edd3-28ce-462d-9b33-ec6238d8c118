import GridViewIcon from "@mui/icons-material/GridView";
import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
import { useLocation } from "react-router-dom";
import { ADMIN_LOWER, PROVIDER_LOWER } from "../constants/constants";

const useMenu = () => {
  const location = useLocation();

  const pathArr = location.pathname
    ?.trim()
    .split("/")
    .filter((path) => path?.length);
  const pathPrefix = (pathArr && pathArr[0]) || "";

  const adminSideMenu = [
    {
      title: "Provider Groups",
      route: "/admin/provider-group",
      icon: <GridViewIcon />,
      hide: false,
      disabled: false,
    },

    {
      title: "Settings",
      route: "/admin/settings",
      icon: <GridViewIcon />,
      hide: false,
      disabled: false,
    },
  ];

  const providerSideMenu = [
    {
      title: "Settings",
      route: "/admin/settings",
      icon: <SettingsOutlinedIcon />,
      hide: false,
      disabled: false,
    },
  ];

  return pathPrefix === ADMIN_LOWER
    ? adminSideMenu
    : pathPrefix === PROVIDER_LOWER
    ? providerSideMenu
    : [];
};

export default useMenu;
