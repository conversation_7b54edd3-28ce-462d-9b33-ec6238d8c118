import { useNavigate } from "react-router-dom";

import { useUserControllerServiceLogout } from "../sdk/queries";
import cookieService from "../services/core/cookie-service";
import storageService from "../services/core/storage-service";

const useLogout = () => {
  const { mutateAsync: logoutMutate, isPending } = useUserControllerServiceLogout();
  const navigate = useNavigate();
  isPending;

  const logout = async () => {
    const refreshToken = storageService.getRefreshToken();
    if (refreshToken) {
      await logoutMutate({
        requestBody: {
          refreshToken,
        },
      });
    }

    cookieService.clearCookies();
    localStorage.clear();
    localStorage.removeItem("redirectURL");

    const loginRoute = "/auth/login";

    navigate(loginRoute);
  };

  return logout;
};

export default useLogout;
